import Foundation
import SwiftUI

@MainActor
class PermissionHandler: ObservableObject {
    static let shared = PermissionHandler()

    @Published var showPermissionAlert = false
    @Published var permissionAlertTitle = ""
    @Published var permissionAlertMessage = ""

    private init() {}
    
    // Note: Camera permissions are not needed when using UIImagePickerController
    // UIImagePickerController handles camera permissions automatically when presented
    
    // Note: Photo library permissions are not needed when using PHPickerViewController
    // PHPickerViewController (iOS 14+) handles privacy automatically without requiring explicit permissions

    private func showPermissionAlert(for feature: String, message: String) {
        permissionAlertTitle = "\(feature) Access Required"
        permissionAlertMessage = message
        showPermissionAlert = true
    }

    func openSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
} 