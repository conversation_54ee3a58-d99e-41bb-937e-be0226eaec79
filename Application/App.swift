import SwiftUI

@main
struct IngredientScannerApp: App {
    @StateObject private var serviceContainer = ServiceContainer.shared
    @StateObject private var appCoordinator: AppCoordinator

    init() {
        // Initialize ServiceContainer first
        let container = ServiceContainer.shared
        _serviceContainer = StateObject(wrappedValue: container)
        _appCoordinator = StateObject(wrappedValue: AppCoordinator(
            pantryService: container.pantryService,
            shoppingListService: container.shoppingListService
        ))
    }

    var body: some Scene {
        WindowGroup {
            serviceContainer.environmentObjects(
                appCoordinator.rootView()
                    .environmentObject(appCoordinator)
            )
        }
    }
}
