import SwiftUI

@main
struct IngredientScannerApp: App {
    @StateObject private var serviceContainer = ServiceContainer.shared
    @StateObject private var appCoordinator: AppCoordinator

    init() {
        // 🔥 CRITICAL: Firebase will be configured in ServiceContainer
        // This ensures proper initialization order
        print("🚀 App initializing...")

        // Initialize ServiceContainer (which will configure Firebase)
        let container = ServiceContainer.shared
        _serviceContainer = StateObject(wrappedValue: container)
        _appCoordinator = StateObject(wrappedValue: AppCoordinator(
            pantryService: container.pantryService,
            shoppingListService: container.shoppingListService
        ))
    }

    var body: some Scene {
        WindowGroup {
            serviceContainer.environmentObjects(
                appCoordinator.rootView()
                    .environmentObject(appCoordinator)
            )
        }
    }
}
