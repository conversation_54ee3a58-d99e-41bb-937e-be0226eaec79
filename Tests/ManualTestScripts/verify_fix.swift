#!/usr/bin/env swift

import Foundation

/// Simple verification script for the food preferences save bug fix
/// This script validates that the key changes have been applied correctly

print("🔍 Debug Team Verification Script")
print("================================")
print("Verifying food preferences save bug fix...")
print()

// Check if the main file exists and contains our fixes
let foodPreferencesPath = "Coordinator/AppCoordinator.swift"

guard let fileContent = try? String(contentsOfFile: foodPreferencesPath) else {
    print("❌ ERROR: Could not read AppCoordinator.swift")
    exit(1)
}

var checksPassedCount = 0
let totalChecks = 6

// Check 1: hasInitialized state variable
if fileContent.contains("@State private var hasInitialized = false") {
    print("✅ Check 1: hasInitialized state variable added")
    checksPassedCount += 1
} else {
    print("❌ Check 1: hasInitialized state variable missing")
}

// Check 2: onAppear initialization guard
if fileContent.contains("if !hasInitialized") && fileContent.contains("hasInitialized = true") {
    print("✅ Check 2: onAppear initialization guard implemented")
    checksPassedCount += 1
} else {
    print("❌ Check 2: onAppear initialization guard missing")
}

// Check 3: Save button with hasChanges logic
if fileContent.contains("Button(\"Save\")") && fileContent.contains("disabled(!hasChanges || isLoading)") {
    print("✅ Check 3: Save button with proper state management implemented")
    checksPassedCount += 1
} else {
    print("❌ Check 3: Save button with proper state management missing")
}

// Check 4: UserDefaults persistence
if fileContent.contains("UserDefaults.standard.set") && fileContent.contains("selectedRestrictions") {
    print("✅ Check 4: UserDefaults persistence implemented")
    checksPassedCount += 1
} else {
    print("❌ Check 4: UserDefaults persistence missing")
}

// Check 5: Haptic feedback
if fileContent.contains("UIImpactFeedbackGenerator") && fileContent.contains("impactOccurred()") {
    print("✅ Check 5: Haptic feedback implemented")
    checksPassedCount += 1
} else {
    print("❌ Check 5: Haptic feedback missing")
}

// Check 6: Async save function
if fileContent.contains("private func saveChanges() async") && fileContent.contains("NotificationCenter.default.post") {
    print("✅ Check 6: Async save function with notifications implemented")
    checksPassedCount += 1
} else {
    print("❌ Check 6: Async save function with notifications missing")
}

// Check 7: New allergy selection interface
if fileContent.contains("struct AllergyCard") && fileContent.contains("availableAllergies") {
    print("✅ Check 7: New allergy selection interface implemented")
    checksPassedCount += 1
} else {
    print("❌ Check 7: New allergy selection interface missing")
}

// Check 8: Profile page data sync
let profilePath = "Features/UserManagement/Views/Profile/UserProfileView.swift"
if let profileContent = try? String(contentsOfFile: profilePath) {
    if profileContent.contains("localDietaryRestrictions.count") && profileContent.contains("onReceive(NotificationCenter.default.publisher") {
        print("✅ Check 8: Profile page data sync implemented")
        checksPassedCount += 1
    } else {
        print("❌ Check 8: Profile page data sync missing")
    }
} else {
    print("❌ Check 8: Could not read UserProfileView.swift")
}

// Check 9: AppCoordinator Quick Stats fix
let coordinatorPath = "Coordinator/AppCoordinator.swift"
if let coordinatorContent = try? String(contentsOfFile: coordinatorPath) {
    if coordinatorContent.contains("coordinator.localDietaryRestrictions.count") && coordinatorContent.contains("coordinator.localAllergies.count") {
        print("✅ Check 9: AppCoordinator Quick Stats using dynamic data")
        checksPassedCount += 1
    } else {
        print("❌ Check 9: AppCoordinator Quick Stats still using hardcoded values")
    }
} else {
    print("❌ Check 9: Could not read AppCoordinator.swift")
}

// Check 10: Family Size dynamic data
if let coordinatorContent = try? String(contentsOfFile: coordinatorPath) {
    if coordinatorContent.contains("coordinator.localFamilySize") && coordinatorContent.contains("@Published var localFamilySize: Int") {
        print("✅ Check 10: Family Size using dynamic data")
        checksPassedCount += 1
    } else {
        print("❌ Check 10: Family Size still hardcoded")
    }
} else {
    print("❌ Check 10: Could not read AppCoordinator.swift for family size check")
}

// Check 11: Family Info notification listening
if let coordinatorContent = try? String(contentsOfFile: coordinatorPath) {
    if coordinatorContent.contains("FamilyInfoUpdated") && coordinatorContent.contains("NotificationCenter.default.addObserver") {
        print("✅ Check 11: Family Info notification listening implemented")
        checksPassedCount += 1
    } else {
        print("❌ Check 11: Family Info notification listening missing")
    }
} else {
    print("❌ Check 11: Could not read AppCoordinator.swift for notification check")
}

// Check 12: RealFamilyInfoView save functionality
if let coordinatorContent = try? String(contentsOfFile: coordinatorPath) {
    if coordinatorContent.contains("saveFamilyInfo()") && coordinatorContent.contains("UserDefaults.standard.set(familySize") {
        print("✅ Check 12: RealFamilyInfoView save functionality implemented")
        checksPassedCount += 1
    } else {
        print("❌ Check 12: RealFamilyInfoView save functionality missing")
    }
} else {
    print("❌ Check 12: Could not read AppCoordinator.swift for save functionality check")
}

print()
print("📊 Verification Results:")
print("========================")
print("Checks passed: \(checksPassedCount)/12")

if checksPassedCount == 12 {
    print("🎉 ALL CHECKS PASSED! The fix has been successfully implemented.")
    print()
    print("🛡️ Debug Team Sign-off:")
    print("- ✅ Scanner (Leo Chen): Bug analyzed and evidence collected")
    print("- ✅ Analyzer (Dr. Aris Thorne): Root cause identified")
    print("- ✅ Architect/Fixer (Morgan Sterling): Fix implemented")
    print("- ✅ Sentinel (Jax Kova): Verification complete")
    print()
    print("📋 Next Steps:")
    print("1. Run manual testing using Tests/ManualTestScripts/FoodPreferencesManualTest.md")
    print("2. Test on physical device if possible")
    print("3. Verify with original bug reporter")
    exit(0)
} else {
    print("⚠️  Some checks failed. Please review the implementation.")
    exit(1)
}
