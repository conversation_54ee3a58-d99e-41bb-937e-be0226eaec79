import SwiftUI
import UIKit

enum NavigationState {
    case staging
    case batchProcessing(images: [UIImage])
    case batchVisionResults(images: [UIImage], visionResponses: [String])
    case batchGeminiProcessing(images: [UIImage], visionResponses: [String])
    #if DEBUG
    case debug(visionResponse: String, geminiResponse: String, ingredients: [Ingredient])
    #endif
    case results([Ingredient])
}

@MainActor
class AppCoordinator: ObservableObject {
    @Published var navigationState: NavigationState = .staging
    @Published var selectedTab: Int = 0 // 0 for Scanner, 1 for Pantry, 2 for Recipes, 3 for Shopping List, 4 for Profile
    private let pantryService: PantryService
    private let shoppingListService: ShoppingListService

    // Local state for UserDefaults data
    @Published var localDietaryRestrictions: [String] = []
    @Published var localAllergies: [String] = []
    @Published var localFamilySize: Int = 4

    init(pantryService: PantryService, shoppingListService: ShoppingListService) {
        self.pantryService = pantryService
        self.shoppingListService = shoppingListService

        // Load initial data
        loadLocalData()

        // Listen for food preferences updates
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("FoodPreferencesUpdated"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.loadLocalData()
            }
        }

        // Listen for family info updates
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("FamilyInfoUpdated"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.loadLocalData()
            }
        }
    }
    
    func rootView() -> some View {
        RootTabView(
            coordinator: self,
            pantryService: pantryService,
            shoppingListService: shoppingListService
        )
    }
    
    // Navigation methods
    func navigateToBatchProcessing(images: [UIImage]) {
        navigationState = .batchProcessing(images: images)
    }
    
    func navigateToBatchVisionResults(images: [UIImage], visionResponses: [String]) {
        navigationState = .batchVisionResults(images: images, visionResponses: visionResponses)
    }
    
    func navigateToBatchGeminiProcessing(images: [UIImage], visionResponses: [String]) {
        navigationState = .batchGeminiProcessing(images: images, visionResponses: visionResponses)
    }
    
    #if DEBUG
    func navigateToDebug(visionResponse: String, geminiResponse: String, ingredients: [Ingredient]) {
        navigationState = .debug(visionResponse: visionResponse, geminiResponse: geminiResponse, ingredients: ingredients)
    }
    #endif
    
    func navigateToResults(ingredients: [Ingredient]) {
        navigationState = .results(ingredients)
    }
    
    func navigateToStaging() {
        navigationState = .staging
    }
    
    func switchToPantryTab() {
        selectedTab = 1
    }

    func switchToPantryTabAndResetScan() {
        selectedTab = 1
        // Reset scan tab to staging after switching
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.resetScanTab()
        }
    }

    func resetScanTab() {
        navigationState = .staging
    }

    func switchToProfileTab() {
        selectedTab = 4
    }

    // MARK: - Data Loading

    private func loadLocalData() {
        if let savedRestrictions = UserDefaults.standard.array(forKey: "selectedRestrictions") as? [String] {
            localDietaryRestrictions = savedRestrictions
        }

        if let savedAllergies = UserDefaults.standard.array(forKey: "allergies") as? [String] {
            localAllergies = savedAllergies
        }

        // Load family size from UserDefaults
        let savedFamilySize = UserDefaults.standard.integer(forKey: "familyMemberCount")
        if savedFamilySize > 0 {
            localFamilySize = savedFamilySize
        }
    }
}

struct RootTabView: View {
    @ObservedObject var coordinator: AppCoordinator
    let pantryService: PantryService
    let shoppingListService: ShoppingListService
    
    var body: some View {
        TabView(selection: $coordinator.selectedTab) {
            scannerTab()
                .tabItem {
                    Label("Scan", systemImage: "barcode.viewfinder")
                }
                .tag(0)
            
            PantryView()
                .tabItem {
                    Label("Pantry", systemImage: "cabinet.fill")
                }
                .tag(1)
            
            RecipeGeneratorView()
                .tabItem {
                    Label("Recipes", systemImage: "flame.fill")
                }
                .tag(2)
            
            ShoppingListView(shoppingListService: shoppingListService)
                .tabItem {
                    Label("Shopping List", systemImage: "list.bullet")
                }
                .tag(3)

            // Profile Tab - User Management
            ProfileTabView(coordinator: coordinator)
                .tabItem {
                    Label("Profile", systemImage: "person.circle")
                }
                .tag(4)
        }
        .environmentObject(pantryService)
        .environmentObject(shoppingListService)
    }
    
    @ViewBuilder
    private func scannerTab() -> some View {
        Group {
            switch coordinator.navigationState {
            case .staging:
                StagingView(viewModel: StagingViewModel(coordinator: coordinator))
            case .batchProcessing(let images):
                BatchProcessingView(viewModel: BatchProcessingViewModel(coordinator: coordinator, images: images))
            case .batchVisionResults(let images, let visionResponses):
                BatchVisionResultsView(viewModel: BatchVisionResultsViewModel(coordinator: coordinator, images: images, visionResponses: visionResponses))
            case .batchGeminiProcessing(let images, let visionResponses):
                BatchGeminiProcessingView(viewModel: BatchGeminiProcessingViewModel(coordinator: coordinator, images: images, visionResponses: visionResponses))
            #if DEBUG
            case .debug(let visionResponse, let geminiResponse, let ingredients):
                DebugView(viewModel: DebugViewModel(coordinator: coordinator, visionResponse: visionResponse, geminiResponse: geminiResponse, ingredients: ingredients))
            #endif
            case .results(let ingredients):
                ResultsView(viewModel: ResultsViewModel(coordinator: coordinator, ingredients: ingredients, pantryService: pantryService))
            }
        }
    }
}

// MARK: - Profile Tab View

struct ProfileTabView: View {
    @ObservedObject var coordinator: AppCoordinator
    @StateObject private var authManager = SimpleAuthManager()
    @State private var showingLogin = false
    @State private var showingFoodPreferences = false
    @State private var showingFamilyInfo = false
    @State private var showingNotifications = false
    @State private var showingAppPreferences = false
    @State private var showingAccountSettings = false

    var body: some View {
        NavigationView {
            if authManager.isAuthenticated {
                authenticatedProfileView
            } else {
                unauthenticatedProfileView
            }
        }
        .sheet(isPresented: $showingLogin) {
            RealLoginView(authManager: authManager)
        }
        .sheet(isPresented: $showingFoodPreferences) {
            FoodPreferencesView()
        }
        .sheet(isPresented: $showingFamilyInfo) {
            RealFamilyInfoView()
        }
        .sheet(isPresented: $showingNotifications) {
            NotificationsView()
        }
        .sheet(isPresented: $showingAppPreferences) {
            AppPreferencesView()
        }
        .sheet(isPresented: $showingAccountSettings) {
            AccountSettingsView()
        }
    }

    private var unauthenticatedProfileView: some View {
        VStack(spacing: 32) {
            // Header
            VStack(spacing: 16) {
                Image(systemName: "person.circle")
                    .font(.system(size: 80))
                    .foregroundColor(.secondary)

                VStack(spacing: 8) {
                    Text("Welcome to Your Profile")
                        .font(.title2.weight(.semibold))

                    Text("Sign in to access your personalized preferences, family settings, and sync your data across devices")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 16)
                }
            }

            // Sign In Button
            Button("Sign In") {
                showingLogin = true
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)

            Spacer()
        }
        .padding(32)
        .navigationTitle("Profile")
        .navigationBarTitleDisplayMode(.large)
    }

    private var authenticatedProfileView: some View {
        ScrollView {
            VStack(spacing: 24) {
                // User Info Section
                userInfoSection

                // Development Configuration
                developmentConfigSection

                // Quick Stats
                quickStatsSection

                // Settings Sections
                settingsSection

                // Sign Out
                signOutSection
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .navigationTitle("Profile")
        .navigationBarTitleDisplayMode(.large)
    }

    private var userInfoSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)

                VStack(alignment: .leading, spacing: 4) {
                    Text(authManager.currentUser?.displayName ?? "User")
                        .font(.title2.weight(.semibold))

                    Text(authManager.currentUser?.email ?? "No email")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack(spacing: 4) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                        Text("Signed in with Skip (Dev)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()
            }

            // Development User ID Display
            VStack(alignment: .leading, spacing: 4) {
                Text("User ID (Development)")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.orange)

                Text("4cc7d2dd-4287-4fdb-ad40-0427a0c962d6")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(8)
                    .background(Color(.tertiarySystemGroupedBackground))
                    .cornerRadius(6)
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }

    private var developmentConfigSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Development Configuration")
                    .font(.headline)
                    .foregroundColor(.orange)
                Spacer()
                Text("DEV ONLY")
                    .font(.caption.weight(.bold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange)
                    .cornerRadius(4)
            }

            VStack(spacing: 8) {
                ConfigRow(label: "Session ID", value: "sess_4cc7d2dd-4287-4fdb")
                ConfigRow(label: "API Endpoint", value: "https://api.dev.ingredientscanner.com")
                ConfigRow(label: "Firebase Project", value: "ingredient-scanner-dev")
                ConfigRow(label: "Build Config", value: "Debug - Development")
                ConfigRow(label: "Feature Flags", value: "UserManagement: ON, Analytics: OFF")
            }
        }
        .padding(16)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }

    private var quickStatsSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Quick Stats")
                    .font(.headline)
                Spacer()
            }

            HStack(spacing: 16) {
                StatCard(title: "Dietary Restrictions", value: "\(coordinator.localDietaryRestrictions.count)", icon: "leaf.fill", color: .green)
                StatCard(title: "Allergies", value: "\(coordinator.localAllergies.count)", icon: "exclamationmark.triangle.fill", color: .orange)
                StatCard(title: "Family Size", value: "\(coordinator.localFamilySize)", icon: "person.2.fill", color: .blue)
            }
        }
    }

    private var settingsSection: some View {
        VStack(spacing: 16) {
            SettingGroup(title: "Food & Family Preferences") {
                SettingRow(
                    title: "Food Preferences",
                    subtitle: "Dietary restrictions, allergies, and exclusions",
                    icon: "leaf.fill",
                    iconColor: .green
                ) {
                    showingFoodPreferences = true
                }

                SettingRow(
                    title: "Family Information",
                    subtitle: "Household size and special dietary needs",
                    icon: "house.fill",
                    iconColor: .blue
                ) {
                    showingFamilyInfo = true
                }
            }

            SettingGroup(title: "App Settings") {
                SettingRow(
                    title: "Notifications",
                    subtitle: "Manage your notification preferences",
                    icon: "bell.fill",
                    iconColor: .orange
                ) {
                    showingNotifications = true
                }

                SettingRow(
                    title: "App Preferences",
                    subtitle: "Language, units, and accessibility",
                    icon: "gear",
                    iconColor: .gray
                ) {
                    showingAppPreferences = true
                }
            }

            SettingGroup(title: "Data & Privacy") {
                SettingRow(
                    title: "Account Settings",
                    subtitle: "Privacy, data, and account management",
                    icon: "lock.shield.fill",
                    iconColor: .purple
                ) {
                    showingAccountSettings = true
                }
            }
        }
    }

    private var signOutSection: some View {
        Button(action: {
            authManager.signOut()
        }) {
            HStack {
                Image(systemName: "rectangle.portrait.and.arrow.right")
                Text("Sign Out")
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.red.opacity(0.1))
            .foregroundColor(.red)
            .cornerRadius(8)
        }
    }
}

// MARK: - Supporting Views

// MARK: - Simple Authentication System
// Fake LoginModalView removed - now using real authentication system

class SimpleAuthManager: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: SimpleUser?

    func signInWithApple() {
        // Simulate Apple Sign In
        currentUser = SimpleUser(displayName: "Apple User", email: "<EMAIL>", provider: "Apple")
        isAuthenticated = true
    }

    func signInWithGoogle() {
        // Simulate Google Sign In
        currentUser = SimpleUser(displayName: "Google User", email: "<EMAIL>", provider: "Google")
        isAuthenticated = true
    }

    func signInWithEmail(email: String) {
        // Simulate Email Sign In
        currentUser = SimpleUser(displayName: "Email User", email: email, provider: "Email")
        isAuthenticated = true
    }

    func signInAsDeveloper() {
        // Simulate Developer Sign In
        currentUser = SimpleUser(displayName: "Developer User", email: "<EMAIL>", provider: "Developer")
        isAuthenticated = true
    }

    func signOut() {
        currentUser = nil
        isAuthenticated = false
    }
}

struct SimpleUser {
    let displayName: String
    let email: String
    let provider: String
}

struct RealLoginView: View {
    @ObservedObject var authManager: SimpleAuthManager
    @Environment(\.dismiss) private var dismiss
    @State private var isLoading = false
    @State private var showingEmailInput = false

    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                VStack(spacing: 16) {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("Sign In")
                        .font(.title.weight(.semibold))
                }

                // All Sign In Buttons
                VStack(spacing: 16) {
                    // Apple Sign In Button
                    Button(action: {
                        isLoading = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            authManager.signInWithApple()
                            isLoading = false
                            dismiss()
                        }
                    }) {
                        HStack {
                            Image(systemName: "applelogo")
                            Text("Continue with Apple")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.black)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .disabled(isLoading)

                    // Google Sign In Button
                    Button(action: {
                        isLoading = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            authManager.signInWithGoogle()
                            isLoading = false
                            dismiss()
                        }
                    }) {
                        HStack {
                            Image(systemName: "globe")
                            Text("Continue with Google")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .disabled(isLoading)

                    // Continue with Email Button
                    Button(action: {
                        showingEmailInput = true
                    }) {
                        HStack {
                            Image(systemName: "envelope")
                            Text("Continue with Email")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .disabled(isLoading)

                    // Developer Button
                    Button(action: {
                        isLoading = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            authManager.signInAsDeveloper()
                            isLoading = false
                            dismiss()
                        }
                    }) {
                        HStack {
                            Image(systemName: "hammer.fill")
                            Text("Developer")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .disabled(isLoading)
                }

                if isLoading {
                    ProgressView("Signing in...")
                        .padding()
                }

                Spacer()
            }
            .padding(32)
            .navigationTitle("Sign In")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingEmailInput) {
                SimpleEmailInputView(authManager: authManager)
            }
        }
    }
}

struct SimpleEmailInputView: View {
    @ObservedObject var authManager: SimpleAuthManager
    @Environment(\.dismiss) private var dismiss
    @State private var email = ""
    @State private var emailError: String?
    @State private var isLoading = false
    @State private var showingPasswordInput = false
    @State private var showingRegistration = false

    private var isEmailValid: Bool {
        !email.isEmpty && emailError == nil
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                VStack(spacing: 16) {
                    Text("We'll check if you have an account, and help create one if you don't")
                        .font(.title3)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                }

                VStack(alignment: .leading, spacing: 12) {
                    Text("Email")
                        .font(.headline)
                        .foregroundColor(.primary)

                    TextField("<EMAIL>", text: $email)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .autocorrectionDisabled()
                        .font(.body)
                        .onChange(of: email) { _ in
                            validateEmail()
                        }

                    if let emailError = emailError {
                        Text(emailError)
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }

                Button(action: {
                    proceedWithEmail()
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }
                        Text("Continue")
                            .font(.headline)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(isEmailValid && !isLoading ? Color.pink : Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(!isEmailValid || isLoading)

                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Continue with Email")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
            .sheet(isPresented: $showingPasswordInput) {
                SimplePasswordInputView(email: email, authManager: authManager)
            }
            .sheet(isPresented: $showingRegistration) {
                SimpleRegistrationView(email: email, authManager: authManager)
            }
        }
    }

    private func validateEmail() {
        emailError = nil

        guard !email.isEmpty else { return }

        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)

        if !emailPredicate.evaluate(with: email) {
            emailError = "Please enter a valid email address"
        }
    }

    private func proceedWithEmail() {
        guard isEmailValid else { return }

        isLoading = true

        // Simulate checking if email exists
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            isLoading = false

            // For demo purposes, assume emails ending with "new" are new users
            if email.contains("new") {
                showingRegistration = true
            } else {
                showingPasswordInput = true
            }
        }
    }
}

struct SimplePasswordInputView: View {
    let email: String
    @ObservedObject var authManager: SimpleAuthManager
    @Environment(\.dismiss) private var dismiss
    @State private var password = ""
    @State private var isPasswordVisible = false
    @State private var isLoading = false

    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                Text("Using \(email)")
                    .font(.title2)
                    .foregroundColor(.primary)

                VStack(alignment: .leading, spacing: 12) {
                    Text("Password")
                        .font(.headline)

                    HStack {
                        Group {
                            if isPasswordVisible {
                                TextField("Enter password", text: $password)
                            } else {
                                SecureField("Enter password", text: $password)
                            }
                        }
                        .font(.body)

                        Button(action: {
                            isPasswordVisible.toggle()
                        }) {
                            Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }

                Button(action: {
                    signIn()
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        }
                        Text("Sign in")
                            .font(.headline)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(!password.isEmpty && !isLoading ? Color.pink : Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .disabled(password.isEmpty || isLoading)

                Button("Forgot password?") {
                    // Handle forgot password
                }
                .font(.body)
                .foregroundColor(.blue)

                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Sign In")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
    }

    private func signIn() {
        isLoading = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            authManager.signInWithEmail(email: email)
            isLoading = false
            dismiss()
        }
    }
}

struct SimpleRegistrationView: View {
    let email: String
    @ObservedObject var authManager: SimpleAuthManager
    @Environment(\.dismiss) private var dismiss
    @State private var username = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var isLoading = false
    @State private var currentStep = 0 // 0: username, 1: password

    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                Text("Using \(email)")
                    .font(.title2)
                    .foregroundColor(.primary)

                if currentStep == 0 {
                    usernameStep
                } else {
                    passwordStep
                }

                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Create account")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        if currentStep > 0 {
                            currentStep -= 1
                        } else {
                            dismiss()
                        }
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
    }

    private var usernameStep: some View {
        VStack(spacing: 24) {
            VStack(alignment: .leading, spacing: 12) {
                Text("Name")
                    .font(.headline)

                HStack {
                    Text("@")
                        .font(.title2)
                        .foregroundColor(.primary)
                        .padding(.leading, 8)

                    TextField("aaaa", text: $username)
                        .font(.body)
                        .autocapitalization(.none)
                        .autocorrectionDisabled()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }

            Button(action: {
                currentStep = 1
            }) {
                Text("Continue")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(username.count >= 3 ? Color.pink : Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
            }
            .disabled(username.count < 3)
        }
    }

    private var passwordStep: some View {
        VStack(spacing: 24) {
            VStack(alignment: .leading, spacing: 12) {
                Text("Password")
                    .font(.headline)

                SecureField("Enter password", text: $password)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }

            VStack(alignment: .leading, spacing: 12) {
                Text("Confirm Password")
                    .font(.headline)

                SecureField("Confirm password", text: $confirmPassword)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }

            Button(action: {
                createAccount()
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    Text("Create Account")
                        .font(.headline)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(isFormValid && !isLoading ? Color.pink : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!isFormValid || isLoading)
        }
    }

    private var isFormValid: Bool {
        password.count >= 6 && password == confirmPassword
    }

    private func createAccount() {
        isLoading = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            authManager.signInWithEmail(email: email) // Simulate account creation + sign in
            isLoading = false
            dismiss()
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title2.weight(.bold))

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
}

struct SettingGroup<Content: View>: View {
    let title: String
    let content: Content

    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text(title)
                    .font(.headline)
                Spacer()
            }

            VStack(spacing: 8) {
                content
            }
        }
    }
}

struct SettingRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let iconColor: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(Color(.secondarySystemGroupedBackground))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ConfigRow: View {
    let label: String
    let value: String

    var body: some View {
        HStack {
            Text(label)
                .font(.caption.weight(.medium))
                .foregroundColor(.primary)
                .frame(width: 100, alignment: .leading)

            Text(value)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding(.vertical, 2)
    }
}

// MARK: - Setting Detail Views

struct FoodPreferencesView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedRestrictions = Set<String>()
    @State private var allergies: [String] = []
    @State private var showingRestrictionPicker = false
    @State private var showingAllergyInput = false
    @State private var newAllergyText = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var hasInitialized = false
    @State private var initialRestrictions = Set<String>()
    @State private var initialAllergies: [String] = []

    // Predefined dietary restrictions
    private let availableRestrictions = [
        "Vegetarian", "Vegan", "Gluten-Free", "Dairy-Free", "Lactose-Free",
        "Nut-Free", "Soy-Free", "Egg-Free", "Fish-Free", "Shellfish-Free",
        "Low-Carb", "Keto", "Paleo", "Mediterranean", "Low-Sodium",
        "Sugar-Free", "Organic Only", "Non-GMO", "Halal", "Kosher"
    ]

    // Check if there are unsaved changes
    private var hasChanges: Bool {
        return selectedRestrictions != initialRestrictions || allergies != initialAllergies
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Error message display
                    if let errorMessage = errorMessage {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.red)
                            Spacer()
                            Button("Dismiss") {
                                self.errorMessage = nil
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                    }

                    // Dietary Restrictions
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Dietary Restrictions")
                            .font(.headline)

                        ForEach(Array(selectedRestrictions).sorted(), id: \.self) { restriction in
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                Text(restriction)
                                Spacer()
                                Button("Remove") {
                                    selectedRestrictions.remove(restriction)
                                }
                                .foregroundColor(.red)
                            }
                            .padding()
                            .background(Color(.secondarySystemGroupedBackground))
                            .cornerRadius(8)
                        }

                        Button("Add Restriction") {
                            showingRestrictionPicker = true
                        }
                        .buttonStyle(.bordered)
                    }

                    // Allergies
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Allergies")
                            .font(.headline)

                        ForEach(allergies, id: \.self) { allergy in
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                Text(allergy)
                                Spacer()
                                Button("Remove") {
                                    allergies.removeAll { $0 == allergy }
                                }
                                .foregroundColor(.red)
                            }
                            .padding()
                            .background(Color(.secondarySystemGroupedBackground))
                            .cornerRadius(8)
                        }

                        Button("Add Allergy") {
                            showingAllergyInput = true
                        }
                        .buttonStyle(.bordered)
                    }
                }
                .padding()
            }
            .navigationTitle("Food Preferences")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .disabled(isLoading)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(!hasChanges || isLoading)
                    .font(.system(size: 17, weight: .semibold))
                }
            }
            .onAppear {
                // Only load preferences on first appearance to avoid overwriting user changes
                if !hasInitialized {
                    loadCurrentPreferences()
                    hasInitialized = true
                }
            }
        }
        .sheet(isPresented: $showingRestrictionPicker) {
            DietaryRestrictionPickerView(
                availableRestrictions: availableRestrictions,
                selectedRestrictions: $selectedRestrictions
            )
        }
        .sheet(isPresented: $showingAllergyInput) {
            AllergyInputView(
                allergies: $allergies,
                newAllergyText: $newAllergyText
            )
        }
    }

    // MARK: - Data Management

    private func loadCurrentPreferences() {
        // For now, load from UserDefaults as a simple persistence solution
        // This will be replaced with proper UserProfileService integration later

        if let savedRestrictions = UserDefaults.standard.array(forKey: "selectedRestrictions") as? [String] {
            selectedRestrictions = Set(savedRestrictions)
            initialRestrictions = Set(savedRestrictions)
        }

        if let savedAllergies = UserDefaults.standard.array(forKey: "allergies") as? [String] {
            allergies = savedAllergies
            initialAllergies = savedAllergies
        }
    }

    private func saveChanges() async {
        isLoading = true
        errorMessage = nil

        do {
            // Save to UserDefaults for now
            UserDefaults.standard.set(Array(selectedRestrictions), forKey: "selectedRestrictions")
            UserDefaults.standard.set(allergies, forKey: "allergies")

            // Update initial values to reflect saved state
            initialRestrictions = selectedRestrictions
            initialAllergies = allergies

            // Simulate async operation
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

            // Provide success feedback
            await MainActor.run {
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)

                // Post notification for other parts of the app to update
                NotificationCenter.default.post(name: NSNotification.Name("FoodPreferencesUpdated"), object: nil)

                // Dismiss on success
                dismiss()
            }

        } catch {
            await MainActor.run {
                errorMessage = "Failed to save preferences: \(error.localizedDescription)"
                let errorFeedback = UINotificationFeedbackGenerator()
                errorFeedback.notificationOccurred(.error)
            }
        }

        isLoading = false
    }
}

// MARK: - Dietary Restriction Picker

struct DietaryRestrictionPickerView: View {
    let availableRestrictions: [String]
    @Binding var selectedRestrictions: Set<String>
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    ForEach(availableRestrictions, id: \.self) { restriction in
                        RestrictionCard(
                            title: restriction,
                            isSelected: selectedRestrictions.contains(restriction)
                        ) {
                            if selectedRestrictions.contains(restriction) {
                                selectedRestrictions.remove(restriction)
                            } else {
                                selectedRestrictions.insert(restriction)
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Select Restrictions")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .font(.system(size: 17, weight: .semibold))
                }
            }
        }
    }
}

struct RestrictionCard: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title2)
                    .foregroundColor(isSelected ? .green : .gray)

                Text(title)
                    .font(.caption.weight(.medium))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .padding(.horizontal, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.green.opacity(0.1) : Color(.secondarySystemGroupedBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.green : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Allergy Input View

struct AllergyInputView: View {
    @Binding var allergies: [String]
    @Binding var newAllergyText: String
    @Environment(\.dismiss) private var dismiss
    @State private var selectedAllergies = Set<String>()

    // Common food allergens based on FDA's "Big 9" and other common allergens
    private let availableAllergies = [
        "Eggs", "Fish", "Milk", "Peanuts", "Sesame", "Shellfish", "Soy", "Tree Nuts", "Wheat",
        // Additional common allergens
        "Almonds", "Brazil Nuts", "Cashews", "Coconut", "Hazelnuts", "Macadamia Nuts", "Pecans",
        "Pine Nuts", "Pistachios", "Walnuts", "Crab", "Lobster", "Shrimp", "Clams", "Mussels",
        "Oysters", "Scallops", "Anchovies", "Bass", "Cod", "Flounder", "Grouper", "Haddock",
        "Halibut", "Herring", "Mahi Mahi", "Perch", "Pike", "Pollock", "Salmon", "Sardines",
        "Snapper", "Sole", "Swordfish", "Trout", "Tuna", "Barley", "Bulgur", "Durum", "Farro",
        "Graham", "Kamut", "Matzo", "Rye", "Semolina", "Spelt", "Triticale"
    ].sorted()

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)

                        Text("Select Allergies")
                            .font(.title2.weight(.semibold))

                        Text("Choose all foods that cause allergic reactions or intolerances")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }

                    // Allergy Grid
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        ForEach(availableAllergies, id: \.self) { allergy in
                            AllergyCard(
                                title: allergy,
                                isSelected: selectedAllergies.contains(allergy)
                            ) {
                                if selectedAllergies.contains(allergy) {
                                    selectedAllergies.remove(allergy)
                                } else {
                                    selectedAllergies.insert(allergy)
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical)
            }
            .navigationTitle("Select Allergies")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        addSelectedAllergies()
                    }
                    .font(.system(size: 17, weight: .semibold))
                    .disabled(selectedAllergies.isEmpty)
                }
            }
            .onAppear {
                // Pre-select already added allergies
                selectedAllergies = Set(allergies.filter { availableAllergies.contains($0) })
            }
        }
    }

    private func addSelectedAllergies() {
        // Add new allergies that aren't already in the list
        for allergy in selectedAllergies {
            if !allergies.contains(allergy) {
                allergies.append(allergy)
            }
        }

        // Remove allergies that were deselected
        allergies.removeAll { allergy in
            availableAllergies.contains(allergy) && !selectedAllergies.contains(allergy)
        }

        // Provide haptic feedback for successful addition
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // Delay dismiss to ensure state update completes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            dismiss()
        }
    }
}

struct AllergyCard: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .orange : .secondary)
                    .font(.title3)

                Text(title)
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(isSelected ? .orange : .primary)

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.orange.opacity(0.1) : Color(.secondarySystemGroupedBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.orange : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct RealFamilyInfoView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var familySize = 4
    @State private var hasChildren = true
    @State private var childrenCount = 2
    @State private var childrenAges: [Int] = [8, 12]
    @State private var showingChildAgeInput = false
    @State private var editingChildIndex: Int?
    @State private var tempAge = ""

    // Computed properties for validation
    private var totalSpecialMembers: Int {
        hasChildren ? childrenCount : 0
    }

    private var remainingMembers: Int {
        familySize - totalSpecialMembers
    }

    private var maxChildren: Int {
        max(0, familySize - 1) // Assume at least 1 adult
    }

    var body: some View {
        NavigationView {
            Form {
                // Family Size Section
                Section("Family Size") {
                    Stepper("Total Members: \(familySize)", value: $familySize, in: 1...10)
                        .onChange(of: familySize) { _ in
                            validateAndAdjustCounts()
                        }

                    if totalSpecialMembers > 0 {
                        HStack {
                            Text("Remaining Adults")
                            Spacer()
                            Text("\(remainingMembers)")
                                .foregroundColor(remainingMembers >= 0 ? .secondary : .red)
                        }
                    }
                }

                // Children Section
                Section("Children (0-17 years)") {
                    Toggle("Has Children", isOn: $hasChildren)
                        .onChange(of: hasChildren) { newValue in
                            if !newValue {
                                childrenCount = 0
                                childrenAges = []
                            } else {
                                childrenCount = min(1, maxChildren)
                                childrenAges = [8]
                            }
                        }

                    if hasChildren {
                        Stepper("Number of Children: \(childrenCount)",
                               value: $childrenCount,
                               in: 1...maxChildren)
                            .onChange(of: childrenCount) { newCount in
                                adjustChildrenAges(to: newCount)
                            }

                        ForEach(0..<childrenCount, id: \.self) { index in
                            HStack {
                                Text("Child \(index + 1) Age")
                                Spacer()
                                Button("\(childrenAges[safe: index] ?? 8) years") {
                                    editingChildIndex = index
                                    tempAge = "\(childrenAges[safe: index] ?? 8)"
                                    showingChildAgeInput = true
                                }
                                .foregroundColor(.blue)
                            }
                        }
                    }
                }

                // Validation Warning
                if remainingMembers < 0 {
                    Section {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                            Text("Total children exceed family size")
                                .foregroundColor(.red)
                                .font(.caption)
                        }
                    }
                }
            }
            .navigationTitle("Family Information")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveFamilyInfo()
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingChildAgeInput) {
            AgeInputView(
                title: "Child Age",
                subtitle: "Enter age (0-17 years)",
                currentAge: $tempAge,
                ageRange: 0...17
            ) { age in
                if let index = editingChildIndex, index < childrenAges.count {
                    childrenAges[index] = age
                }
                editingChildIndex = nil
            }
        }
    }

    // MARK: - Helper Methods

    private func validateAndAdjustCounts() {
        // Adjust children count if it exceeds the limit
        if hasChildren && childrenCount > maxChildren {
            childrenCount = maxChildren
            adjustChildrenAges(to: childrenCount)
        }
    }

    private func adjustChildrenAges(to count: Int) {
        if count > childrenAges.count {
            // Add new children with default age
            let newAges = Array(repeating: 8, count: count - childrenAges.count)
            childrenAges.append(contentsOf: newAges)
        } else if count < childrenAges.count {
            // Remove excess children
            childrenAges = Array(childrenAges.prefix(count))
        }
    }



    private func saveFamilyInfo() {
        // Save to UserDefaults
        UserDefaults.standard.set(familySize, forKey: "familyMemberCount")
        UserDefaults.standard.set(hasChildren, forKey: "familyHasChildren")
        UserDefaults.standard.set(childrenAges, forKey: "familyChildrenAges")

        // Send notification for UI updates
        NotificationCenter.default.post(name: NSNotification.Name("FamilyInfoUpdated"), object: nil)

        // Haptic feedback
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
    }
}

// MARK: - Age Input View

struct AgeInputView: View {
    let title: String
    let subtitle: String
    @Binding var currentAge: String
    let ageRange: ClosedRange<Int>
    let onSave: (Int) -> Void

    @Environment(\.dismiss) private var dismiss
    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                VStack(spacing: 16) {
                    Image(systemName: "person.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)

                    Text(title)
                        .font(.title2.weight(.semibold))

                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                VStack(spacing: 12) {
                    TextField("Age", text: $currentAge)
                        .textFieldStyle(.roundedBorder)
                        .keyboardType(.numberPad)
                        .focused($isTextFieldFocused)
                        .onSubmit {
                            saveAge()
                        }

                    Text("Valid range: \(ageRange.lowerBound) - \(ageRange.upperBound) years")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Button("Save") {
                    saveAge()
                }
                .buttonStyle(.borderedProminent)
                .disabled(!isValidAge)

                Spacer()
            }
            .padding(24)
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                isTextFieldFocused = true
            }
        }
    }

    private var isValidAge: Bool {
        guard let age = Int(currentAge.trimmingCharacters(in: .whitespacesAndNewlines)) else {
            return false
        }
        return ageRange.contains(age)
    }

    private func saveAge() {
        guard let age = Int(currentAge.trimmingCharacters(in: .whitespacesAndNewlines)),
              ageRange.contains(age) else {
            return
        }
        onSave(age)
        dismiss()
    }
}

// MARK: - Array Extension for Safe Access

extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

struct NotificationsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var recipeNotifications = true
    @State private var shoppingReminders = true
    @State private var expirationAlerts = false

    var body: some View {
        NavigationView {
            Form {
                Section("Recipe Notifications") {
                    Toggle("New Recipe Suggestions", isOn: $recipeNotifications)
                    Toggle("Meal Planning Reminders", isOn: $shoppingReminders)
                }

                Section("Pantry Notifications") {
                    Toggle("Expiration Alerts", isOn: $expirationAlerts)
                    Toggle("Low Stock Warnings", isOn: $shoppingReminders)
                }

                Section("Frequency") {
                    HStack {
                        Text("Notification Time")
                        Spacer()
                        Text("6:00 PM")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct AppPreferencesView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedLanguage = "English"
    @State private var selectedUnits = "Metric"
    @State private var enableHaptics = true

    var body: some View {
        NavigationView {
            Form {
                Section("Language & Region") {
                    HStack {
                        Text("Language")
                        Spacer()
                        Text(selectedLanguage)
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Measurement Units")
                        Spacer()
                        Text(selectedUnits)
                            .foregroundColor(.secondary)
                    }
                }

                Section("Accessibility") {
                    Toggle("Haptic Feedback", isOn: $enableHaptics)

                    HStack {
                        Text("Text Size")
                        Spacer()
                        Text("Medium")
                            .foregroundColor(.secondary)
                    }
                }

                Section("Development") {
                    HStack {
                        Text("App Version")
                        Spacer()
                        Text("1.0.0 (Dev)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Build Number")
                        Spacer()
                        Text("4cc7d2dd")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("App Preferences")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct AccountSettingsView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Form {
                Section("Account Information") {
                    HStack {
                        Text("User ID")
                        Spacer()
                        Text("4cc7d2dd-4287-4fdb-ad40-0427a0c962d6")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Account Type")
                        Spacer()
                        Text("Development")
                            .foregroundColor(.orange)
                    }

                    HStack {
                        Text("Data Sync")
                        Spacer()
                        Text("Enabled")
                            .foregroundColor(.green)
                    }
                }

                Section("Privacy") {
                    HStack {
                        Text("Data Collection")
                        Spacer()
                        Text("Minimal")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Analytics")
                        Spacer()
                        Text("Disabled")
                            .foregroundColor(.secondary)
                    }
                }

                Section("Danger Zone") {
                    Button("Export Data") {
                        // Export action
                    }
                    .foregroundColor(.blue)

                    Button("Delete Account") {
                        // Delete action
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Account Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}