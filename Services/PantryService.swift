import Foundation
import SwiftUI

@MainActor
class PantryService: ObservableObject {
    @Published var pantryItems: [Ingredient] = []
    @Published var recentlyAddedItems: Set<UUID> = []

    func addIngredients(_ newIngredients: [Ingredient]) {
        // Add only unique ingredients based on name
        for ingredient in newIngredients {
            if !pantryItems.contains(where: { $0.name.lowercased() == ingredient.name.lowercased() }) {
                pantryItems.append(ingredient)
            }
        }
    }

    func markAsRecentlyAdded(_ ingredients: [Ingredient]) {
        // Clear previous recently added items
        recentlyAddedItems.removeAll()
        // Mark new items as recently added
        for ingredient in ingredients {
            if let pantryItem = pantryItems.first(where: { $0.name.lowercased() == ingredient.name.lowercased() }) {
                recentlyAddedItems.insert(pantryItem.id)
            }
        }

        // Clear recently added status after 5 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
            self?.recentlyAddedItems.removeAll()
        }
    }

    func addIngredient(_ ingredient: Ingredient) {
        if !pantryItems.contains(where: { $0.name.lowercased() == ingredient.name.lowercased() }) {
            pantryItems.append(ingredient)
        }
    }

    func deletePantryItem(at offsets: IndexSet) {
        pantryItems.remove(atOffsets: offsets)
    }

    func deleteIngredient(_ ingredient: Ingredient) {
        pantryItems.removeAll { $0.id == ingredient.id }
    }

    func isRecentlyAdded(_ ingredient: Ingredient) -> Bool {
        return recentlyAddedItems.contains(ingredient.id)
    }

    func updateIngredient(_ ingredient: Ingredient, newName: String, newCategory: PantryCategory) {
        if let index = pantryItems.firstIndex(where: { $0.id == ingredient.id }) {
            pantryItems[index].name = newName
            pantryItems[index].category = newCategory
        }
    }

    func deleteIngredients(_ ingredients: [Ingredient]) {
        for ingredient in ingredients {
            deleteIngredient(ingredient)
        }
    }
}