import Foundation

actor RecipeGenerationService {
    private let geminiService = GeminiAPIService()
    
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [Recipe] {
        let prompt = constructMealIdeasPrompt(ingredients: ingredients, preferences: preferences)
        
        do {
            let jsonResponse = try await geminiService.processText(prompt)
            
            // Extract JSON from the response
            guard let jsonData = extractJSON(from: jsonResponse) else {
                throw RecipeGenerationError.invalidJSONResponse
            }
            
            let decoder = JSONDecoder()
            let response = try decoder.decode(RecipeListResponse.self, from: jsonData)
            return response.recipes
        } catch let error as DecodingError {
            print("Decoding error: \(error)")
            throw RecipeGenerationError.decodingFailed(error.localizedDescription)
        } catch {
            throw error
        }
    }
    
    private func constructMealIdeasPrompt(ingredients: [String], preferences: RecipePreferences) -> String {
        let ingredientsList = ingredients.joined(separator: ", ")
        let restrictionsList = preferences.dietaryRestrictions.isEmpty ? "None" : preferences.dietaryRestrictions.joined(separator: ", ")
        
        return """
        You are an expert culinary AI. Your task is to create 3-5 diverse recipe ideas based on available ingredients and user preferences.
        
        **Important Rules:**
        1. Generate exactly 3-5 different recipes.
        2. Include 2-3 recipes that can be made with ONLY the available ingredients (plus basic pantry staples like oil, salt, pepper, water).
        3. Include 1-2 recipes that require 1-3 additional ingredients NOT in the available list. These should be common, easy-to-find items.
        4. Ensure variety in cuisine types and cooking methods.
        5. Adhere strictly to all dietary restrictions.
        6. Your response MUST be a single valid JSON object. Do not include any text or markdown before or after the JSON.
        
        **JSON Output Structure:**
        {
          "recipes": [
            {
              "recipeTitle": "String",
              "description": "A brief, enticing description of the dish.",
              "ingredients": ["Complete list including quantities"],
              "instructions": ["Step by step instructions"],
              "nutrition": {
                "calories": "String (e.g., '350')",
                "protein": "String (e.g., '25g')",
                "carbs": "String (e.g., '40g')",
                "fat": "String (e.g., '15g')"
              }
            }
          ]
        }
        
        ---
        **Available Ingredients:**
        \(ingredientsList)
        
        **User Preferences:**
        {
          "cookingTimeInMinutes": \(preferences.cookingTimeInMinutes),
          "numberOfServings": \(preferences.numberOfServings),
          "dietaryRestrictions": [\(restrictionsList)]
        }
        ---
        
        Now, generate 3-5 diverse recipe ideas as a valid JSON object.
        """
    }
    
    private func extractJSON(from text: String) -> Data? {
        // First, try to parse the entire response as JSON
        if let data = text.data(using: .utf8) {
            do {
                // Test if it's valid JSON
                _ = try JSONSerialization.jsonObject(with: data, options: [])
                return data
            } catch {
                // If not, try to extract JSON from the text
            }
        }
        
        // Try to find JSON object in the text
        if let startRange = text.range(of: "{"),
           let endRange = text.range(of: "}", options: .backwards),
           startRange.lowerBound <= endRange.lowerBound {
            let jsonString = String(text[startRange.lowerBound...endRange.upperBound])
            return jsonString.data(using: .utf8)
        }
        
        return nil
    }
}

enum RecipeGenerationError: LocalizedError {
    case invalidJSONResponse
    case decodingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidJSONResponse:
            return "The AI response did not contain valid JSON"
        case .decodingFailed(let message):
            return "Failed to decode recipes: \(message)"
        }
    }
} 