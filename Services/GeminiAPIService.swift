import Foundation

actor GeminiAPIService {
    private let apiKey = APIKeys.geminiAPIKey
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"
    
    func extractIngredients(from text: String) async throws -> [Ingredient] {
        // Check if API key is configured
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw APIError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        let prompt = """
        Analyze the following text (which may include receipt text and detected food items) and extract ONLY EDIBLE FOOD AND BEVERAGE items. Return ONLY a JSON array with no other text.

        CRITICAL NAMING RULES:
        - Remove ONLY quantities, units, and measurements (no "dozen", "lb", "gallon", "pack", "bag", "2x", "12 count", etc.)
        - Keep important food descriptors (type, variety, preparation method) but NOT size descriptors
        - Remove brand names when possible, but keep descriptive food names
        - Prefer plural forms when appropriate: "Blueberry" → "Blueberries", "Egg" → "Eggs"
        - Examples: "Dozen Large Eggs" → "Eggs", "1 Gallon Whole Milk" → "Whole Milk", "2lb Ground Beef" → "Ground Beef"

        CONSOLIDATION RULES:
        - If same item appears multiple times, include only ONCE
        - Consolidate similar items into single entries with most descriptive name:
          * "Blueberry" + "Blueberries" → "Blueberries"
          * "Egg" + "Large Eggs" + "Dozen Eggs" → "Eggs"
          * "Milk" + "Whole Milk" → "Whole Milk"
        - Keep type/variety descriptors: "Whole Milk", "Brown Rice", "Greek Yogurt"
        - Remove size descriptors: "Large", "Medium", "Small", "Jumbo"

        EXCLUDE non-food items:
        - Cleaning products, personal care, household items, pet supplies, vitamins, kitchen containers

        PROCESSING DETECTED ITEMS:
        - When you see "Detected Items:" section, these are visually identified objects from the image
        - Focus on food-related detected items and incorporate them with receipt text
        - Use detected items to supplement or clarify unclear receipt text
        - Combine both sources for comprehensive ingredient extraction

        CATEGORY DEFINITIONS (use these exact strings):

        "Produce" - Fresh fruits, vegetables, herbs:
        Examples: Apples, Bananas, Spinach, Tomatoes, Onions, Garlic, Cilantro

        "Proteins" - Meat, poultry, fish, eggs, tofu:
        Examples: Chicken Breast, Ground Beef, Salmon, Eggs, Tofu, Turkey

        "Dairy & Alternatives" - Milk, cheese, yogurt, non-dairy alternatives:
        Examples: Milk, Cheddar Cheese, Greek Yogurt, Almond Milk, Butter

        "Pastry" - Ready-to-eat baked goods, bread, bagels:
        Examples: 7 Grain Bread, Whole Wheat Bagels, Croissants, Blueberry Muffins, Donuts, Dinner Rolls

        "Baking & Sweeteners" - Raw baking ingredients only:
        Examples: All Purpose Flour, Granulated Sugar, Baking Powder, Vanilla Extract, Honey, Brown Sugar

        "Dry Goods" - Uncooked rice, pasta, beans, grains, cereals:
        Examples: White Rice, Brown Rice, Pasta, Black Beans, Quinoa, Steel Cut Oats, Cereal

        "Packaged Foods" - Pre-cooked, processed, or ready-to-heat foods:
        Examples: Rice Cakes, Microwaveable Rice, Canned Tomatoes, Chicken Stock, Frozen Vegetables, Instant Oatmeal

        "Oils, Vinegars & Condiments" - Cooking oils, sauces, dressings:
        Examples: Olive Oil, Soy Sauce, Ketchup, Mayonnaise, Balsamic Vinegar

        "Spices & Seasonings" - Herbs, spices, salt, pepper:
        Examples: Salt, Black Pepper, Garlic Powder, Oregano, Cumin



        "Snacks & Beverages" - Drinks, chips, snacks, coffee, tea:
        Examples: Coffee, Tea, Chips, Crackers, Juice, Soda

        "Other" - Any edible item that doesn't fit above categories

        REQUIRED JSON FORMAT:
        [
          { "name": "clean ingredient name", "category": "exact category string" }
        ]

        NAMING EXAMPLES:
        - "Dozen Large Eggs" → "Eggs" (remove size + quantity)
        - "1 Gallon Whole Milk" → "Whole Milk" (keep type, remove quantity)
        - "2lb Ground Turkey" → "Ground Turkey" (keep preparation)
        - "Dave's Killer 7 Grain Bread" → "7 Grain Bread" (keep variety)
        - "Uncle Ben's Microwaveable Rice" → "Microwaveable Rice" (keep preparation)
        - "Small Blueberry + Large Blueberries" → "Blueberries" (consolidate, prefer plural)

        CRITICAL CATEGORIZATION RULES:
        1. CONSOLIDATE duplicates - if same item appears multiple times, include only once
        2. Remove ONLY quantities/units, keep descriptive food names (but not sizes)
        3. Prefer plural forms: "Blueberries", "Eggs", "Tomatoes"
        4. Rice Cakes → "Packaged Foods" (processed snack)
        5. Plain Rice → "Dry Goods" (uncooked grain)
        6. Microwaveable Rice → "Packaged Foods" (pre-cooked, ready-to-heat)
        7. Bread/baked goods → "Pastry" (not "Baking & Sweeteners")
        8. Raw baking ingredients → "Baking & Sweeteners"
        9. Keep food types: "Whole Milk", "7 Grain Bread", "Brown Rice"
        10. Return ONLY the JSON array, no other text


        TEXT TO ANALYZE:

        \(text)
        """
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 403 {
                throw APIError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
            } else {
                throw APIError.invalidResponse
            }
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let text = parts.first?["text"] as? String else {
            throw APIError.parsingError
        }
        
        // Parse the JSON response
        let cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // Debug: Print the raw response to help diagnose issues
        print("🔍 Gemini Raw Response:")
        print(cleanedText)
        print("🔍 End of Raw Response")

        // Extract JSON from the response (handle markdown code blocks)
        guard let jsonData = extractJSON(from: cleanedText) else {
            print("❌ Failed to extract JSON from response")
            throw APIError.parsingError
        }

        // Create a temporary structure to decode the JSON response
        struct IngredientResponse: Codable {
            let name: String
            let category: String
        }

        do {
            let ingredientResponses = try JSONDecoder().decode([IngredientResponse].self, from: jsonData)

            // Convert to Ingredient objects with proper PantryCategory
            let ingredients = ingredientResponses.compactMap { response -> Ingredient? in
                guard let category = PantryCategory(rawValue: response.category) else {
                    // If the category doesn't match any enum case, use .other
                    print("⚠️ Unknown category '\(response.category)' for ingredient '\(response.name)'")
                    print("📝 Available categories: \(PantryCategory.allCases.map { $0.rawValue }.joined(separator: ", "))")
                    print("🔄 Using 'Other' category as fallback")
                    return Ingredient(name: response.name, category: .other)
                }
                print("✅ Categorized '\(response.name)' as '\(response.category)'")
                return Ingredient(name: response.name, category: category)
            }

            print("✅ Successfully parsed \(ingredients.count) ingredients")
            // Apply client-side consolidation as safety net
            let consolidatedIngredients = consolidateIngredients(ingredients)

            return consolidatedIngredients
        } catch {
            // If JSON parsing fails, log the error and throw parsing error
            print("❌ JSON parsing failed: \(error)")
            print("📝 Raw response was: \(cleanedText)")
            throw APIError.parsingError
        }
    }

    // MARK: - Client-Side Consolidation (Safety Net)

    private func consolidateIngredients(_ ingredients: [Ingredient]) -> [Ingredient] {
        var consolidated: [String: Ingredient] = [:]

        for ingredient in ingredients {
            let normalizedName = normalizeIngredientName(ingredient.name)

            if let existing = consolidated[normalizedName] {
                // Keep the more descriptive name
                let betterName = chooseBetterName(existing.name, ingredient.name)
                consolidated[normalizedName] = Ingredient(
                    name: betterName,
                    category: ingredient.category,
                    isSelected: ingredient.isSelected
                )
            } else {
                consolidated[normalizedName] = ingredient
            }
        }

        let result = Array(consolidated.values)

        // Log consolidation if duplicates were found
        if result.count < ingredients.count {
            print("🔄 Consolidated \(ingredients.count) items into \(result.count) unique ingredients")
        }

        return result
    }

    private func normalizeIngredientName(_ name: String) -> String {
        let lowercased = name.lowercased()

        // Handle common plural/singular variations
        let pluralMappings: [String: String] = [
            "blueberry": "blueberries",
            "strawberry": "strawberries",
            "raspberry": "raspberries",
            "blackberry": "blackberries",
            "egg": "eggs",
            "tomato": "tomatoes",
            "potato": "potatoes",
            "avocado": "avocados",
            "banana": "bananas",
            "apple": "apples",
            "orange": "oranges",
            "onion": "onions",
            "carrot": "carrots"
        ]

        // Check if this is a singular form that should be plural
        for (singular, plural) in pluralMappings {
            if lowercased == singular || lowercased == plural {
                return plural
            }
        }

        // Remove size descriptors for normalization
        let sizeDescriptors = ["large", "medium", "small", "jumbo", "extra large", "xl"]
        var normalized = lowercased

        for size in sizeDescriptors {
            normalized = normalized.replacingOccurrences(of: size + " ", with: "")
            normalized = normalized.replacingOccurrences(of: " " + size, with: "")
        }

        return normalized.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    private func chooseBetterName(_ name1: String, _ name2: String) -> String {
        // Prefer the more descriptive name (longer, more specific)

        // If one contains the other, prefer the longer one
        if name1.lowercased().contains(name2.lowercased()) {
            return name1
        } else if name2.lowercased().contains(name1.lowercased()) {
            return name2
        }

        // Prefer names with type descriptors
        let typeDescriptors = ["whole", "brown", "white", "greek", "organic", "fresh"]
        let name1HasType = typeDescriptors.contains { name1.lowercased().contains($0) }
        let name2HasType = typeDescriptors.contains { name2.lowercased().contains($0) }

        if name1HasType && !name2HasType {
            return name1
        } else if name2HasType && !name1HasType {
            return name2
        }

        // Default to the longer name (more descriptive)
        return name1.count >= name2.count ? name1 : name2
    }
    
    func processText(_ prompt: String) async throws -> String {
        // Check if API key is configured
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw APIError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 403 {
                throw APIError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
            } else {
                throw APIError.invalidResponse
            }
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let text = parts.first?["text"] as? String else {
            throw APIError.parsingError
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    // Helper method to extract JSON from text that might contain markdown code blocks
    private func extractJSON(from text: String) -> Data? {
        let cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // First, try to parse the entire response as JSON
        if let data = cleanedText.data(using: .utf8) {
            do {
                // Test if it's valid JSON
                _ = try JSONSerialization.jsonObject(with: data, options: [])
                return data
            } catch {
                // If not, try to extract JSON from the text
            }
        }

        // Try to find JSON array in markdown code blocks
        if let startRange = cleanedText.range(of: "```json"),
           let endRange = cleanedText.range(of: "```", range: startRange.upperBound..<cleanedText.endIndex) {
            let jsonString = String(cleanedText[startRange.upperBound..<endRange.lowerBound])
                .trimmingCharacters(in: .whitespacesAndNewlines)
            return jsonString.data(using: .utf8)
        }

        // Try to find JSON array without markdown
        if let startRange = cleanedText.range(of: "["),
           let endRange = cleanedText.range(of: "]", options: .backwards),
           startRange.lowerBound <= endRange.lowerBound {
            let jsonString = String(cleanedText[startRange.lowerBound...endRange.upperBound])
            return jsonString.data(using: .utf8)
        }

        return nil
    }
}