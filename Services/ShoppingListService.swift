import Foundation
import SwiftUI

@MainActor
class ShoppingListService: ObservableObject {
    @Published var shoppingItems: [ShoppingItem] = []
    
    func addItem(_ itemName: String) {
        // Check if item already exists (case insensitive)
        let exists = shoppingItems.contains { item in
            item.name.lowercased() == itemName.lowercased()
        }
        
        if !exists {
            let newItem = ShoppingItem(name: itemName)
            shoppingItems.append(newItem)
        }
    }
    
    func toggleItem(at index: Int) {
        guard index < shoppingItems.count else { return }
        shoppingItems[index].isChecked.toggle()
    }
    
    func deleteItem(at offsets: IndexSet) {
        shoppingItems.remove(atOffsets: offsets)
    }
    
    func clearCheckedItems() {
        shoppingItems.removeAll { $0.isChecked }
    }
    
    var uncheckedItemsCount: Int {
        shoppingItems.filter { !$0.isChecked }.count
    }
} 