import Foundation
import UIKit

actor GoogleVisionAPIService {
    private let apiKey = APIKeys.googleVisionAPIKey
    private let baseURL = "https://vision.googleapis.com/v1/images:annotate"

    struct VisionResult {
        let detectedText: String
        let detectedLabels: [String]

        var combinedContent: String {
            var content = detectedText
            if !detectedLabels.isEmpty {
                content += "\n\nDetected Items: " + detectedLabels.joined(separator: ", ")
            }
            return content
        }
    }

    func detectTextAndLabels(in image: UIImage) async throws -> VisionResult {
        // Check if API key is configured
        guard apiKey != "YOUR_GOOGLE_API_KEY_HERE" && !apiKey.isEmpty else {
            throw APIError.apiKeyNotConfigured("Google Vision API key not configured. Please update APIKeys.swift")
        }
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw APIError.invalidImage
        }
        
        let base64Image = imageData.base64EncodedString()
        
        let requestBody: [String: Any] = [
            "requests": [
                [
                    "image": [
                        "content": base64Image
                    ],
                    "features": [
                        [
                            "type": "TEXT_DETECTION",
                            "maxResults": 1
                        ],
                        [
                            "type": "LABEL_DETECTION",
                            "maxResults": 20
                        ]
                    ]
                ]
            ]
        ]
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 403 {
                throw APIError.apiKeyNotConfigured("Google Vision API key is invalid or access denied (403)")
            } else {
                throw APIError.invalidResponse
            }
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let responses = json["responses"] as? [[String: Any]],
              let firstResponse = responses.first else {
            throw APIError.invalidResponse
        }

        // Extract text detection results
        var detectedText = ""
        if let textAnnotations = firstResponse["textAnnotations"] as? [[String: Any]],
           let fullText = textAnnotations.first?["description"] as? String {
            detectedText = fullText
        }

        // Extract label detection results
        var detectedLabels: [String] = []
        if let labelAnnotations = firstResponse["labelAnnotations"] as? [[String: Any]] {
            detectedLabels = labelAnnotations.compactMap { annotation in
                guard let description = annotation["description"] as? String,
                      let score = annotation["score"] as? Double,
                      score > 0.5 else { return nil } // Only include labels with >50% confidence
                return description
            }
        }

        // Filter labels to focus on food-related items
        let foodRelatedLabels = detectedLabels.filter { label in
            let lowercased = label.lowercased()
            return lowercased.contains("food") ||
                   lowercased.contains("fruit") ||
                   lowercased.contains("vegetable") ||
                   lowercased.contains("meat") ||
                   lowercased.contains("dairy") ||
                   lowercased.contains("bread") ||
                   lowercased.contains("grain") ||
                   lowercased.contains("beverage") ||
                   lowercased.contains("drink") ||
                   lowercased.contains("ingredient") ||
                   lowercased.contains("produce") ||
                   lowercased.contains("grocery") ||
                   lowercased.contains("package") ||
                   lowercased.contains("bottle") ||
                   lowercased.contains("can") ||
                   lowercased.contains("box")
        }

        // Check if we found any useful content
        if detectedText.isEmpty && foodRelatedLabels.isEmpty {
            throw APIError.noContentFound
        }

        return VisionResult(detectedText: detectedText, detectedLabels: foodRelatedLabels)
    }

    // Backward compatibility method for existing code
    func detectText(in image: UIImage) async throws -> String {
        let result = try await detectTextAndLabels(in: image)
        return result.combinedContent
    }
}

enum APIError: LocalizedError {
    case invalidImage
    case invalidURL
    case invalidResponse
    case noTextFound
    case noContentFound
    case parsingError
    case apiKeyNotConfigured(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "Could not process the selected image"
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid response from server. Please check your API keys."
        case .noTextFound:
            return "No text found in image"
        case .noContentFound:
            return "No text or food items detected in image"
        case .parsingError:
            return "Could not parse the response"
        case .apiKeyNotConfigured(let message):
            return message
        }
    }
} 