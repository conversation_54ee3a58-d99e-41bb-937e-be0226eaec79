import Foundation
import SwiftUI

@MainActor
class ServiceContainer: ObservableObject {
    static let shared = ServiceContainer()

    // Core services
    let pantryService = PantryService()
    let shoppingListService = ShoppingListService()

    // API services (actors)
    let googleVisionService = GoogleVisionAPIService()
    let geminiService = GeminiAPIService()
    let recipeGenerationService = RecipeGenerationService()

    // User management services will be added later when properly configured

    private init() {
        // Private initializer to enforce singleton pattern
    }

    // Convenience method to inject all services into environment
    func environmentObjects<Content: View>(_ content: Content) -> some View {
        content
            .environmentObject(pantryService)
            .environmentObject(shoppingListService)
    }
}
