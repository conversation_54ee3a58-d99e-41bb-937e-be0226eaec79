import Foundation

enum PantryCategory: String, Codable, CaseIterable {
    case bakingAndSweeteners = "Baking & Sweeteners"
    case oilsVinegarsAndCondiments = "Oils, Vinegars & Condiments"
    case spicesAndSeasonings = "Spices & Seasonings"
    case dryGoods = "Dry Goods"
    case packagedFoods = "Packaged Foods"
    case dairyAndAlternatives = "Dairy & Alternatives"
    case proteins = "Proteins"
    case produce = "Produce"
    case pastry = "Pastry"
    case snacksAndBeverages = "Snacks & Beverages"
    case other = "Other" // A fallback category

    var icon: String {
        switch self {
        case .produce:
            return "🥕"
        case .proteins:
            return "🍗"
        case .dairyAndAlternatives:
            return "🥛"
        case .bakingAndSweeteners:
            return "🍯"
        case .oilsVinegarsAndCondiments:
            return "🫒"
        case .spicesAndSeasonings:
            return "🧂"
        case .dryGoods:
            return "🍚"
        case .packagedFoods:
            return "🥫"
        case .pastry:
            return "🥐"
        case .snacksAndBeverages:
            return "🍿"
        case .other:
            return "📦"
        }
    }
}