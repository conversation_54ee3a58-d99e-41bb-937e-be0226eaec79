import Foundation

struct Ingredient: Identifiable, Equatable, Codable {
    let id: UUID
    var name: String
    var category: PantryCategory
    var isSelected: Bool = true
    
    init(name: String, category: PantryCategory = .other, isSelected: Bool = true, id: UUID = UUID()) {
        self.id = id
        self.name = name
        self.category = category
        self.isSelected = isSelected
    }
} 