import Foundation

struct RecipePreferences {
    var cookingTimeInMinutes: Int
    var numberOfServings: Int
    var dietaryRestrictions: [String] // e.g., ["Vegetarian", "Gluten-Free"]
}

struct Recipe: Codable, Identifiable {
    let id = UUID()
    let recipeTitle: String
    let description: String
    let ingredients: [String]
    let instructions: [String]
    let nutrition: NutritionInfo
    
    enum CodingKeys: String, CodingKey {
        case recipeTitle, description, ingredients, instructions, nutrition
    }
    
    struct NutritionInfo: Codable {
        let calories: String
        let protein: String
        let carbs: String
        let fat: String
    }
}

struct RecipeIdea: Identifiable {
    let id = UUID()
    let recipe: Recipe
    var status: RecipeStatus
    var missingIngredients: [String]
    
    enum RecipeStatus {
        case readyToCook
        case almostThere
    }
}

struct RecipeListResponse: Codable {
    let recipes: [Recipe]
}

struct ShoppingItem: Identifiable {
    let id = UUID()
    var name: String
    var isChecked: Bool = false
    var addedDate: Date = Date()
} 