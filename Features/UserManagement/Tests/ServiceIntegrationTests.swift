import XCTest
import Combine
@testable import IngredientScanner

/// Comprehensive integration tests for UserManagement services
/// 
/// Tests the interaction between AuthenticationService, UserProfileService,
/// and UserDataSyncService to ensure proper data flow and error handling.
@MainActor
final class UserManagementServiceIntegrationTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var authService: AuthenticationService!
    private var profileService: UserProfileService!
    private var syncService: UserDataSyncService!
    private var cancellables: Set<AnyCancellable>!
    
    // MARK: - Setup & Teardown
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize services
        authService = AuthenticationService()
        profileService = UserProfileService()
        syncService = UserDataSyncService(
            authService: authService,
            profileService: profileService
        )
        cancellables = Set<AnyCancellable>()
        
        // Wait for initial state
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    override func tearDown() async throws {
        cancellables?.removeAll()
        authService = nil
        profileService = nil
        syncService = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Authentication Service Tests
    
    func testAuthenticationServiceInitialization() {
        XCTAssertNotNil(authService)
        XCTAssertFalse(authService.isAuthenticated)
        XCTAssertNil(authService.currentUser)
        XCTAssertFalse(authService.isLoading)
    }
    
    func testAuthenticationStateTransitions() {
        let expectation = XCTestExpectation(description: "Auth state changes")
        var stateChanges: [AuthenticationState] = []
        
        authService.$authenticationState
            .sink { state in
                stateChanges.append(state)
                if stateChanges.count >= 2 {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // Trigger a state change (this would normally be done by Firebase)
        // For testing, we'll verify the initial state
        XCTAssertEqual(stateChanges.first, .loading)
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testEmailValidation() async {
        // Test invalid email format
        await authService.signInWithEmail("invalid-email", password: "password123")
        
        // Should result in an error state
        XCTAssertTrue(authService.authenticationState.error != nil)
        XCTAssertFalse(authService.isAuthenticated)
    }
    
    // MARK: - UserProfile Service Tests
    
    func testUserProfileServiceInitialization() {
        XCTAssertNotNil(profileService)
        XCTAssertNil(profileService.userProfile)
        XCTAssertFalse(profileService.isLoading)
        XCTAssertNil(profileService.lastError)
    }
    
    func testUserProfileCreation() async {
        let testUserId = "test-user-123"
        let testProfile = UserProfile(
            id: testUserId,
            email: "<EMAIL>",
            displayName: "Test User"
        )
        
        // This test would require Firebase emulator for full integration
        // For now, we test the data structure
        XCTAssertEqual(testProfile.id, testUserId)
        XCTAssertEqual(testProfile.email, "<EMAIL>")
        XCTAssertEqual(testProfile.displayName, "Test User")
        XCTAssertEqual(testProfile.version, 1)
    }
    
    func testUserProfileFoodPreferences() {
        var profile = UserProfile(id: "test")
        profile.strictExclusions = ["peanuts"]
        profile.allergies = ["shellfish"]
        profile.dietaryRestrictions = [.vegetarian]
        
        // Test ingredient exclusion logic
        XCTAssertTrue(profile.shouldExcludeIngredient("peanuts"))
        XCTAssertTrue(profile.shouldExcludeIngredient("shellfish"))
        XCTAssertTrue(profile.shouldExcludeIngredient("chicken")) // vegetarian
        XCTAssertFalse(profile.shouldExcludeIngredient("tomatoes"))
    }
    
    func testUserProfileVersioning() {
        var profile = UserProfile(id: "test")
        let originalVersion = profile.version
        let originalUpdateTime = profile.updatedAt
        
        // Simulate update
        profile.markAsUpdated()
        
        XCTAssertEqual(profile.version, originalVersion + 1)
        XCTAssertGreaterThan(profile.updatedAt, originalUpdateTime)
    }
    
    // MARK: - Data Sync Service Tests
    
    func testDataSyncServiceInitialization() {
        XCTAssertNotNil(syncService)
        XCTAssertEqual(syncService.syncState, .idle)
        XCTAssertNil(syncService.lastSyncDate)
        XCTAssertFalse(syncService.isSyncing)
    }
    
    func testSyncStateTransitions() {
        // Test sync state properties
        XCTAssertFalse(syncService.isSyncing)
        XCTAssertNil(syncService.currentError)
        
        // Test different sync states
        let idleState = SyncState.idle
        let syncingState = SyncState.syncing
        let errorState = SyncState.error(.networkError)
        
        XCTAssertEqual(idleState, .idle)
        XCTAssertEqual(syncingState, .syncing)
        XCTAssertEqual(errorState, .error(.networkError))
    }
    
    func testDataSyncErrorHandling() {
        let authError = DataSyncError.authenticationFailed("Test error")
        let profileError = DataSyncError.profileSyncFailed("Profile error")
        let networkError = DataSyncError.networkError
        
        XCTAssertNotNil(authError.errorDescription)
        XCTAssertNotNil(authError.recoverySuggestion)
        XCTAssertNotNil(profileError.errorDescription)
        XCTAssertNotNil(networkError.errorDescription)
        
        // Test error equality
        XCTAssertEqual(authError, .authenticationFailed("Test error"))
        XCTAssertNotEqual(authError, profileError)
    }
    
    // MARK: - Service Integration Tests
    
    func testServiceDependencyInjection() {
        // Verify that sync service has proper references
        XCTAssertNotNil(syncService)
        
        // Test that services can communicate
        // (This would be more comprehensive with actual Firebase integration)
        let testUser = User(id: "test", email: "<EMAIL>")
        XCTAssertEqual(testUser.id, "test")
        XCTAssertEqual(testUser.bestDisplayName, "test")
    }
    
    func testClearLocalUserData() async {
        // Test data clearing functionality
        await syncService.clearLocalUserData()
        
        XCTAssertEqual(syncService.syncState, .idle)
        XCTAssertNil(syncService.lastSyncDate)
    }
    
    // MARK: - Error Handling Tests
    
    func testAuthenticationErrorMapping() {
        let service = AuthenticationService()
        
        // Test error descriptions
        let networkError = AuthenticationError.networkError
        let invalidCredentials = AuthenticationError.invalidCredentials
        let userNotFound = AuthenticationError.userNotFound
        
        XCTAssertNotNil(networkError.errorDescription)
        XCTAssertNotNil(networkError.recoverySuggestion)
        XCTAssertNotNil(invalidCredentials.errorDescription)
        XCTAssertNotNil(userNotFound.errorDescription)
    }
    
    func testUserProfileErrorHandling() {
        let documentNotFound = UserProfileError.documentNotFound
        let invalidData = UserProfileError.invalidData
        let networkError = UserProfileError.networkError
        
        XCTAssertNotNil(documentNotFound.errorDescription)
        XCTAssertNotNil(documentNotFound.recoverySuggestion)
        XCTAssertNotNil(invalidData.errorDescription)
        XCTAssertNotNil(networkError.errorDescription)
    }
    
    // MARK: - Thread Safety Tests
    
    func testConcurrentAccess() async {
        // Test that services handle concurrent access properly
        let tasks = (0..<10).map { index in
            Task {
                let profile = UserProfile(id: "test-\(index)")
                return profile.bestDisplayName
            }
        }
        
        let results = await withTaskGroup(of: String.self) { group in
            for task in tasks {
                group.addTask {
                    await task.value
                }
            }
            
            var names: [String] = []
            for await name in group {
                names.append(name)
            }
            return names
        }
        
        XCTAssertEqual(results.count, 10)
        XCTAssertTrue(results.allSatisfy { $0.starts(with: "test-") })
    }
    
    // MARK: - Performance Tests
    
    func testUserProfilePerformance() {
        measure {
            // Test profile creation performance
            for i in 0..<1000 {
                let profile = UserProfile(id: "test-\(i)")
                _ = profile.shouldExcludeIngredient("test-ingredient")
            }
        }
    }
    
    func testIngredientExclusionPerformance() {
        var profile = UserProfile(id: "test")
        profile.strictExclusions = Array(0..<100).map { "ingredient-\($0)" }
        profile.allergies = Array(100..<200).map { "allergen-\($0)" }
        profile.dietaryRestrictions = [.vegetarian, .glutenFree, .dairyFree]
        
        measure {
            for i in 0..<1000 {
                _ = profile.shouldExcludeIngredient("test-ingredient-\(i)")
            }
        }
    }
}
