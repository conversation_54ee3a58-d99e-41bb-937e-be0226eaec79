import XCTest
import SwiftUI
import ViewInspector
@testable import IngredientScanner

/// Comprehensive UI and flow tests for login functionality
/// 
/// Tests the complete user authentication flow including:
/// - Login view interactions
/// - Signup view interactions
/// - Form validation
/// - Error handling
/// - Accessibility compliance
@MainActor
final class LoginFlowTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var authService: AuthenticationService!
    private var profileService: UserProfileService!
    private var syncService: UserDataSyncService!
    private var loginViewModel: LoginViewModel!
    
    // MARK: - Setup & Teardown
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize services
        authService = AuthenticationService()
        profileService = UserProfileService()
        syncService = UserDataSyncService(
            authService: authService,
            profileService: profileService
        )
        loginViewModel = LoginViewModel(authService: authService)
        
        // Wait for initial state
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    override func tearDown() async throws {
        authService = nil
        profileService = nil
        syncService = nil
        loginViewModel = nil
        
        try await super.tearDown()
    }
    
    // MARK: - LoginViewModel Tests
    
    func testLoginViewModelInitialization() {
        XCTAssertNotNil(loginViewModel)
        XCTAssertFalse(loginViewModel.isAuthenticated)
        XCTAssertNil(loginViewModel.currentUser)
        XCTAssertFalse(loginViewModel.isLoading)
        XCTAssertTrue(loginViewModel.loginEmail.isEmpty)
        XCTAssertTrue(loginViewModel.loginPassword.isEmpty)
    }
    
    func testLoginFormValidation() {
        // Test empty form
        XCTAssertFalse(loginViewModel.isLoginFormValid)
        
        // Test with email only
        loginViewModel.loginEmail = "<EMAIL>"
        XCTAssertFalse(loginViewModel.isLoginFormValid)
        
        // Test with password only
        loginViewModel.loginEmail = ""
        loginViewModel.loginPassword = "password123"
        XCTAssertFalse(loginViewModel.isLoginFormValid)
        
        // Test with both email and password
        loginViewModel.loginEmail = "<EMAIL>"
        loginViewModel.loginPassword = "password123"
        loginViewModel.validateLoginForm()
        XCTAssertTrue(loginViewModel.isLoginFormValid)
    }
    
    func testEmailValidation() {
        // Test valid emails
        let validEmails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in validEmails {
            loginViewModel.loginEmail = email
            loginViewModel.validateLoginEmail()
            XCTAssertNil(loginViewModel.loginEmailError, "Email \(email) should be valid")
        }
        
        // Test invalid emails
        let invalidEmails = [
            "invalid-email",
            "@example.com",
            "test@",
            "test.example.com"
        ]
        
        for email in invalidEmails {
            loginViewModel.loginEmail = email
            loginViewModel.validateLoginEmail()
            XCTAssertNotNil(loginViewModel.loginEmailError, "Email \(email) should be invalid")
        }
    }
    
    func testPasswordValidation() {
        // Test login password (minimum 6 characters)
        loginViewModel.loginPassword = "12345"
        loginViewModel.validateLoginPassword()
        XCTAssertNotNil(loginViewModel.loginPasswordError)
        
        loginViewModel.loginPassword = "123456"
        loginViewModel.validateLoginPassword()
        XCTAssertNil(loginViewModel.loginPasswordError)
        
        // Test signup password (minimum 8 characters + strength)
        loginViewModel.signupPassword = "1234567"
        loginViewModel.validateSignupPassword()
        XCTAssertNotNil(loginViewModel.signupPasswordError)
        
        loginViewModel.signupPassword = "StrongPass123!"
        loginViewModel.validateSignupPassword()
        XCTAssertNil(loginViewModel.signupPasswordError)
    }
    
    func testSignupFormValidation() {
        // Test empty form
        XCTAssertFalse(loginViewModel.isSignupFormValid)
        
        // Fill form step by step
        loginViewModel.signupEmail = "<EMAIL>"
        XCTAssertFalse(loginViewModel.isSignupFormValid)
        
        loginViewModel.signupPassword = "StrongPass123!"
        XCTAssertFalse(loginViewModel.isSignupFormValid)
        
        loginViewModel.signupConfirmPassword = "StrongPass123!"
        XCTAssertFalse(loginViewModel.isSignupFormValid) // Still need terms
        
        loginViewModel.agreedToTerms = true
        loginViewModel.validateSignupForm()
        XCTAssertTrue(loginViewModel.isSignupFormValid)
    }
    
    func testPasswordConfirmationValidation() {
        loginViewModel.signupPassword = "password123"
        loginViewModel.signupConfirmPassword = "different123"
        loginViewModel.validateSignupConfirmPassword()
        XCTAssertNotNil(loginViewModel.signupConfirmPasswordError)
        
        loginViewModel.signupConfirmPassword = "password123"
        loginViewModel.validateSignupConfirmPassword()
        XCTAssertNil(loginViewModel.signupConfirmPasswordError)
    }
    
    func testPasswordStrengthEvaluation() {
        let weakPassword = "123456"
        let fairPassword = "password123"
        let goodPassword = "Password123"
        let strongPassword = "StrongPass123!"
        
        XCTAssertEqual(PasswordStrength.evaluate(weakPassword), .weak)
        XCTAssertEqual(PasswordStrength.evaluate(fairPassword), .fair)
        XCTAssertEqual(PasswordStrength.evaluate(goodPassword), .good)
        XCTAssertEqual(PasswordStrength.evaluate(strongPassword), .strong)
    }
    
    // MARK: - UI Component Tests
    
    func testLoginViewInitialization() throws {
        let loginView = LoginView()
            .environmentObject(authService)
            .environmentObject(AppPreferences())
        
        // Test that view can be created without errors
        XCTAssertNotNil(loginView)
    }
    
    func testSignUpViewInitialization() throws {
        let signUpView = SignUpView()
            .environmentObject(authService)
            .environmentObject(AppPreferences())
        
        // Test that view can be created without errors
        XCTAssertNotNil(signUpView)
    }
    
    func testAuthenticationContainerViewInitialization() throws {
        let containerView = AuthenticationContainerView()
            .environmentObject(authService)
            .environmentObject(syncService)
            .environmentObject(AppPreferences())
        
        // Test that view can be created without errors
        XCTAssertNotNil(containerView)
    }
    
    // MARK: - Authentication Flow Tests
    
    func testAuthenticationStateTransitions() async {
        let expectation = XCTestExpectation(description: "Auth state changes")
        var stateChanges: [AuthenticationState] = []
        
        // Monitor authentication state changes
        let cancellable = authService.$authenticationState
            .sink { state in
                stateChanges.append(state)
                if stateChanges.count >= 2 {
                    expectation.fulfill()
                }
            }
        
        // Trigger state change (mock)
        await authService.signInWithEmail("<EMAIL>", password: "wrongpassword")
        
        await fulfillment(of: [expectation], timeout: 3.0)
        
        // Verify state transitions occurred
        XCTAssertGreaterThan(stateChanges.count, 1)
        
        cancellable.cancel()
    }
    
    func testFormClearingOnSignOut() async {
        // Fill login form
        loginViewModel.loginEmail = "<EMAIL>"
        loginViewModel.loginPassword = "password123"
        
        // Fill signup form
        loginViewModel.signupEmail = "<EMAIL>"
        loginViewModel.signupPassword = "StrongPass123!"
        loginViewModel.signupConfirmPassword = "StrongPass123!"
        loginViewModel.agreedToTerms = true
        
        // Simulate sign out
        await loginViewModel.signOut()
        
        // Verify forms are cleared
        XCTAssertTrue(loginViewModel.loginEmail.isEmpty)
        XCTAssertTrue(loginViewModel.loginPassword.isEmpty)
        XCTAssertTrue(loginViewModel.signupEmail.isEmpty)
        XCTAssertTrue(loginViewModel.signupPassword.isEmpty)
        XCTAssertTrue(loginViewModel.signupConfirmPassword.isEmpty)
        XCTAssertFalse(loginViewModel.agreedToTerms)
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorStateHandling() {
        // Test authentication error handling
        let networkError = AuthenticationError.networkError
        loginViewModel.authenticationError = networkError
        
        XCTAssertEqual(loginViewModel.authenticationError, networkError)
        XCTAssertNotNil(loginViewModel.authenticationError?.errorDescription)
        XCTAssertNotNil(loginViewModel.authenticationError?.recoverySuggestion)
    }
    
    func testFormErrorClearing() {
        // Set some errors
        loginViewModel.loginEmailError = "Invalid email"
        loginViewModel.loginPasswordError = "Too short"
        loginViewModel.signupEmailError = "Already exists"
        
        // Clear errors
        loginViewModel.clearErrors()
        
        // Verify all errors are cleared
        XCTAssertNil(loginViewModel.loginEmailError)
        XCTAssertNil(loginViewModel.loginPasswordError)
        XCTAssertNil(loginViewModel.signupEmailError)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityLabels() {
        // Test that password strength has proper accessibility
        let weakStrength = PasswordStrength.weak
        let strongStrength = PasswordStrength.strong
        
        XCTAssertEqual(weakStrength.text, "Weak")
        XCTAssertEqual(strongStrength.text, "Strong")
        
        // Test that colors are appropriate
        XCTAssertEqual(weakStrength.color, .red)
        XCTAssertEqual(strongStrength.color, .green)
    }
    
    // MARK: - Performance Tests
    
    func testFormValidationPerformance() {
        measure {
            for i in 0..<1000 {
                loginViewModel.loginEmail = "test\(i)@example.com"
                loginViewModel.validateLoginEmail()
            }
        }
    }
    
    func testPasswordStrengthPerformance() {
        let passwords = (0..<100).map { "password\($0)!" }
        
        measure {
            for password in passwords {
                _ = PasswordStrength.evaluate(password)
            }
        }
    }
    
    // MARK: - Integration Tests
    
    func testViewModelServiceIntegration() {
        // Test that view model properly integrates with auth service
        XCTAssertNotNil(loginViewModel.authService)
        XCTAssertEqual(loginViewModel.isAuthenticated, authService.isAuthenticated)
        XCTAssertEqual(loginViewModel.isLoading, authService.isLoading)
    }
}
