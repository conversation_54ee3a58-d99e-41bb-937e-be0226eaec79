import XCTest
@testable import IngredientScanner

/// Comprehensive unit tests for UserManagement models
/// 
/// Tests all data models for correctness, edge cases, and Swift 6.0 compliance
/// Following TDD principles with clear test organization
final class UserManagementModelTests: XCTestCase {
    
    // MARK: - User Model Tests
    
    func testUserInitializationFromFirebaseUser() {
        // Test User initialization with mock Firebase data
        // This would require Firebase Auth mock, so we test the manual init instead
        let user = User(
            id: "test-uid-123",
            email: "<EMAIL>",
            displayName: "Test User",
            photoURL: "https://example.com/photo.jpg",
            isEmailVerified: true
        )
        
        XCTAssertEqual(user.id, "test-uid-123")
        XCTAssertEqual(user.email, "<EMAIL>")
        XCTAssertEqual(user.displayName, "Test User")
        XCTAssertEqual(user.photoURL, "https://example.com/photo.jpg")
        XCTAssertTrue(user.isEmailVerified)
    }
    
    func testUserBestDisplayName() {
        // Test with display name
        let userWithDisplayName = User(id: "1", displayName: "John <PERSON>e")
        XCTAssertEqual(userWithDisplayName.bestDisplayName, "John Doe")
        
        // Test with email only
        let userWithEmail = User(id: "2", email: "<EMAIL>")
        XCTAssertEqual(userWithEmail.bestDisplayName, "john")
        
        // Test with neither
        let userMinimal = User(id: "3")
        XCTAssertEqual(userMinimal.bestDisplayName, "User")
    }
    
    func testUserPrimaryProvider() {
        let appleUser = User(id: "1", email: "<EMAIL>")
        XCTAssertEqual(appleUser.primaryProvider, .apple)
        
        let googleUser = User(id: "2", email: "<EMAIL>")
        XCTAssertEqual(googleUser.primaryProvider, .google)
        
        let emailUser = User(id: "3", email: "<EMAIL>")
        XCTAssertEqual(emailUser.primaryProvider, .email)
        
        let anonymousUser = User(id: "4")
        XCTAssertEqual(anonymousUser.primaryProvider, .anonymous)
    }
    
    // MARK: - AuthenticationState Tests
    
    func testAuthenticationStateProperties() {
        let user = User(id: "test", email: "<EMAIL>")
        
        let loadingState = AuthenticationState.loading
        XCTAssertTrue(loadingState.isLoading)
        XCTAssertFalse(loadingState.isAuthenticated)
        XCTAssertNil(loadingState.user)
        
        let authenticatedState = AuthenticationState.authenticated(user)
        XCTAssertFalse(authenticatedState.isLoading)
        XCTAssertTrue(authenticatedState.isAuthenticated)
        XCTAssertEqual(authenticatedState.user?.id, "test")
        
        let unauthenticatedState = AuthenticationState.unauthenticated
        XCTAssertFalse(unauthenticatedState.isLoading)
        XCTAssertFalse(unauthenticatedState.isAuthenticated)
        XCTAssertNil(unauthenticatedState.user)
        
        let errorState = AuthenticationState.error(.networkError)
        XCTAssertFalse(errorState.isLoading)
        XCTAssertFalse(errorState.isAuthenticated)
        XCTAssertEqual(errorState.error, .networkError)
    }
    
    // MARK: - UserProfile Tests
    
    func testUserProfileInitialization() {
        let profile = UserProfile(
            id: "test-user",
            email: "<EMAIL>",
            displayName: "Test User"
        )
        
        XCTAssertEqual(profile.id, "test-user")
        XCTAssertEqual(profile.email, "<EMAIL>")
        XCTAssertEqual(profile.displayName, "Test User")
        XCTAssertTrue(profile.strictExclusions.isEmpty)
        XCTAssertTrue(profile.dietaryRestrictions.isEmpty)
        XCTAssertTrue(profile.allergies.isEmpty)
        XCTAssertEqual(profile.version, 1)
    }
    
    func testUserProfileBestDisplayName() {
        let profileWithName = UserProfile(id: "1", displayName: "John Doe")
        XCTAssertEqual(profileWithName.bestDisplayName, "John Doe")
        
        let profileWithEmail = UserProfile(id: "2", email: "<EMAIL>")
        XCTAssertEqual(profileWithEmail.bestDisplayName, "john")
        
        let profileMinimal = UserProfile(id: "3")
        XCTAssertEqual(profileMinimal.bestDisplayName, "User")
    }
    
    func testUserProfileIngredientExclusion() {
        var profile = UserProfile(id: "test")
        profile.strictExclusions = ["peanuts", "shellfish"]
        profile.allergies = ["milk", "eggs"]
        profile.dietaryRestrictions = [.vegetarian, .glutenFree]
        
        // Test strict exclusions
        XCTAssertTrue(profile.shouldExcludeIngredient("peanuts"))
        XCTAssertTrue(profile.shouldExcludeIngredient("Peanuts")) // Case insensitive
        
        // Test allergies
        XCTAssertTrue(profile.shouldExcludeIngredient("milk"))
        XCTAssertTrue(profile.shouldExcludeIngredient("EGGS")) // Case insensitive
        
        // Test dietary restrictions
        XCTAssertTrue(profile.shouldExcludeIngredient("chicken")) // Vegetarian restriction
        XCTAssertTrue(profile.shouldExcludeIngredient("wheat bread")) // Gluten-free restriction
        
        // Test allowed ingredients
        XCTAssertFalse(profile.shouldExcludeIngredient("tomatoes"))
        XCTAssertFalse(profile.shouldExcludeIngredient("rice"))
    }
    
    func testUserProfileVersioning() {
        var profile = UserProfile(id: "test")
        let originalVersion = profile.version
        let originalUpdateTime = profile.updatedAt
        
        // Small delay to ensure time difference
        Thread.sleep(forTimeInterval: 0.01)
        
        profile.markAsUpdated()
        
        XCTAssertEqual(profile.version, originalVersion + 1)
        XCTAssertGreaterThan(profile.updatedAt, originalUpdateTime)
    }
    
    // MARK: - DietaryRestriction Tests
    
    func testDietaryRestrictionExclusions() {
        // Test vegetarian restrictions
        XCTAssertTrue(DietaryRestriction.vegetarian.excludesIngredient("chicken"))
        XCTAssertTrue(DietaryRestriction.vegetarian.excludesIngredient("beef"))
        XCTAssertFalse(DietaryRestriction.vegetarian.excludesIngredient("cheese"))
        
        // Test vegan restrictions
        XCTAssertTrue(DietaryRestriction.vegan.excludesIngredient("chicken"))
        XCTAssertTrue(DietaryRestriction.vegan.excludesIngredient("cheese"))
        XCTAssertTrue(DietaryRestriction.vegan.excludesIngredient("milk"))
        XCTAssertFalse(DietaryRestriction.vegan.excludesIngredient("vegetables"))
        
        // Test gluten-free restrictions
        XCTAssertTrue(DietaryRestriction.glutenFree.excludesIngredient("wheat"))
        XCTAssertTrue(DietaryRestriction.glutenFree.excludesIngredient("barley"))
        XCTAssertFalse(DietaryRestriction.glutenFree.excludesIngredient("rice"))
        
        // Test dairy-free restrictions
        XCTAssertTrue(DietaryRestriction.dairyFree.excludesIngredient("milk"))
        XCTAssertTrue(DietaryRestriction.dairyFree.excludesIngredient("cheese"))
        XCTAssertFalse(DietaryRestriction.dairyFree.excludesIngredient("chicken"))
    }
    
    // MARK: - FamilyInfo Tests
    
    func testFamilyInfoInitialization() {
        let familyInfo = FamilyInfo()
        XCTAssertEqual(familyInfo.memberCount, 1)
        XCTAssertFalse(familyInfo.hasChildren)
        XCTAssertTrue(familyInfo.childrenAges.isEmpty)
        
        let familyWithChildren = FamilyInfo(
            memberCount: 4,
            hasChildren: true,
            childrenAges: [5, 8, 12]
        )
        XCTAssertEqual(familyWithChildren.memberCount, 4)
        XCTAssertTrue(familyWithChildren.hasChildren)
        XCTAssertEqual(familyWithChildren.childrenAges, [5, 8, 12])
    }
    
    func testFamilyInfoChildAgeCalculations() {
        let family = FamilyInfo(
            memberCount: 5,
            hasChildren: true,
            childrenAges: [2, 7, 14]
        )
        
        XCTAssertEqual(family.youngestChildAge, 2)
        XCTAssertEqual(family.oldestChildAge, 14)
        XCTAssertTrue(family.hasToddlers)
        XCTAssertTrue(family.hasSchoolAgeChildren)
        XCTAssertTrue(family.hasTeenagers)
    }
    
    func testFamilyInfoServingSizeMultiplier() {
        // Single person
        let single = FamilyInfo(memberCount: 1)
        XCTAssertEqual(single.servingSizeMultiplier, 1.0, accuracy: 0.1)
        
        // Couple
        let couple = FamilyInfo(memberCount: 2)
        XCTAssertEqual(couple.servingSizeMultiplier, 2.0, accuracy: 0.1)
        
        // Family with toddler (should be less than member count)
        let familyWithToddler = FamilyInfo(
            memberCount: 3,
            hasChildren: true,
            childrenAges: [2]
        )
        XCTAssertLessThan(familyWithToddler.servingSizeMultiplier, 3.0)
        XCTAssertGreaterThanOrEqual(familyWithToddler.servingSizeMultiplier, 1.0)
    }

    // MARK: - NotificationSettings Tests

    func testNotificationSettingsDefaults() {
        let settings = NotificationSettings()

        // Test sensible defaults
        XCTAssertTrue(settings.ingredientExpiryReminders)
        XCTAssertEqual(settings.expiryReminderDays, 2)
        XCTAssertTrue(settings.lowStockAlerts)
        XCTAssertTrue(settings.shoppingListReminders)
        XCTAssertTrue(settings.recipeRecommendations)
        XCTAssertFalse(settings.marketingEmails) // Privacy-conscious default
    }

    func testNotificationSettingsValidation() {
        // Test expiry reminder days validation (should be 1-7)
        let settingsWithInvalidDays = NotificationSettings(expiryReminderDays: 10)
        XCTAssertLessThanOrEqual(settingsWithInvalidDays.expiryReminderDays, 7)
        XCTAssertGreaterThanOrEqual(settingsWithInvalidDays.expiryReminderDays, 1)
    }

    func testNotificationTime() {
        let time = NotificationTime(hour: 14, minute: 30)
        XCTAssertEqual(time.hour, 14)
        XCTAssertEqual(time.minute, 30)
        XCTAssertEqual(time.formattedTime24Hour, "14:30")

        // Test validation
        let invalidTime = NotificationTime(hour: 25, minute: 70)
        XCTAssertLessThanOrEqual(invalidTime.hour, 23)
        XCTAssertLessThanOrEqual(invalidTime.minute, 59)
    }

    // MARK: - AppPreferences Tests

    func testAppPreferencesDefaults() {
        let preferences = AppPreferences()

        XCTAssertEqual(preferences.fontSize, .medium)
        XCTAssertEqual(preferences.language, .english)
        XCTAssertEqual(preferences.measurementUnit, .metric)
        XCTAssertTrue(preferences.autoSavePreferences)
        XCTAssertFalse(preferences.shareUsageData) // Privacy-conscious default
    }

    func testFontSizeScaling() {
        XCTAssertEqual(FontSize.small.scaleFactor, 0.9, accuracy: 0.01)
        XCTAssertEqual(FontSize.medium.scaleFactor, 1.0, accuracy: 0.01)
        XCTAssertEqual(FontSize.large.scaleFactor, 1.2, accuracy: 0.01)
        XCTAssertEqual(FontSize.extraLarge.scaleFactor, 1.4, accuracy: 0.01)
    }

    func testMeasurementUnitConversion() {
        let preferences = AppPreferences(measurementUnit: .imperial)

        // Test basic formatting (this is a simplified test)
        let formatted = preferences.formatMeasurement(100, unit: "g")
        XCTAssertTrue(formatted.contains("oz"))
    }

    // MARK: - Codable Tests (Critical for Firebase)

    func testUserCodable() throws {
        let user = User(
            id: "test-123",
            email: "<EMAIL>",
            displayName: "Test User",
            isEmailVerified: true
        )

        let encoded = try JSONEncoder().encode(user)
        let decoded = try JSONDecoder().decode(User.self, from: encoded)

        XCTAssertEqual(user, decoded)
    }

    func testUserProfileCodable() throws {
        var profile = UserProfile(id: "test", email: "<EMAIL>")
        profile.strictExclusions = ["peanuts"]
        profile.dietaryRestrictions = [.vegetarian, .glutenFree]
        profile.allergies = ["milk"]

        let encoded = try JSONEncoder().encode(profile)
        let decoded = try JSONDecoder().decode(UserProfile.self, from: encoded)

        XCTAssertEqual(profile, decoded)
    }

    func testFamilyInfoCodable() throws {
        let familyInfo = FamilyInfo(
            memberCount: 4,
            hasChildren: true,
            childrenAges: [5, 8],
            specialDietaryNeeds: ["gluten-free"]
        )

        let encoded = try JSONEncoder().encode(familyInfo)
        let decoded = try JSONDecoder().decode(FamilyInfo.self, from: encoded)

        XCTAssertEqual(familyInfo, decoded)
    }

    // MARK: - Thread Safety Tests (Swift 6.0 Sendable)

    func testSendableCompliance() {
        // This test ensures our models are Sendable and can be safely passed between actors
        let user = User(id: "test")
        let profile = UserProfile(id: "test")
        let familyInfo = FamilyInfo()
        let notifications = NotificationSettings()
        let preferences = AppPreferences()

        // If these compile without warnings, Sendable compliance is working
        Task {
            let _ = user
            let _ = profile
            let _ = familyInfo
            let _ = notifications
            let _ = preferences
        }

        XCTAssertTrue(true) // Test passes if compilation succeeds
    }
}
