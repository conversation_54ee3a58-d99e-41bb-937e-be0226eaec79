import XCTest
import <PERSON><PERSON>
@testable import IngredientScanner

/// Comprehensive UI tests for the complete Profile interface
/// 
/// Tests all Profile sections and their detailed interfaces to ensure
/// the complete user experience works as expected.
/// 
/// Test Coverage:
/// - Complete Profile structure validation
/// - All preference interfaces functionality
/// - User interaction flows
/// - Accessibility compliance
/// - Performance benchmarks
@MainActor
final class CompleteUITests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var authService: AuthenticationService!
    private var profileService: UserProfileService!
    private var syncService: UserDataSyncService!
    private var appPreferences: AppPreferences!
    
    // MARK: - Setup & Teardown
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize services
        authService = AuthenticationService()
        profileService = UserProfileService()
        syncService = UserDataSyncService(
            authService: authService,
            profileService: profileService
        )
        appPreferences = AppPreferences()
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    override func tearDown() async throws {
        authService = nil
        profileService = nil
        syncService = nil
        appPreferences = nil
        
        try await super.tearDown()
    }
    
    // MARK: - Profile Structure Tests
    
    func testCompleteProfileStructure() {
        // Test that UserProfileView can be created with all dependencies
        let profileView = UserProfileView()
            .environmentObject(authService)
            .environmentObject(profileService)
            .environmentObject(syncService)
            .environmentObject(appPreferences)
        
        XCTAssertNotNil(profileView)
    }
    
    func testProfileSectionComponents() {
        // Test ProfileSection component
        let section = ProfileSection(
            title: "Test Section",
            icon: "gear",
            iconColor: .blue
        ) {
            Text("Test Content")
        }
        
        XCTAssertNotNil(section)
    }
    
    func testPreferenceRowComponent() {
        let row = PreferenceRow(
            title: "Test Setting",
            subtitle: "Test description",
            value: "Test value",
            icon: "gear",
            iconColor: .blue,
            fontScale: 1.0
        ) {
            // Test action
        }
        
        XCTAssertNotNil(row)
    }
    
    // MARK: - Food Preferences Tests
    
    func testFoodPreferencesViewCreation() {
        let foodPrefsView = FoodPreferencesView()
            .environmentObject(profileService)
            .environmentObject(appPreferences)
        
        XCTAssertNotNil(foodPrefsView)
    }
    
    func testDietaryRestrictionSelection() {
        // Test all dietary restrictions are available
        let allRestrictions = DietaryRestriction.allCases
        XCTAssertGreaterThan(allRestrictions.count, 5)
        
        // Test each restriction has proper display properties
        for restriction in allRestrictions {
            XCTAssertFalse(restriction.displayName.isEmpty)
            XCTAssertFalse(restriction.iconName.isEmpty)
            XCTAssertNotNil(restriction.color)
        }
    }
    
    func testExclusionTagComponent() {
        let tag = ExclusionTag(
            text: "peanuts",
            color: .red,
            fontScale: 1.0
        ) {
            // Test removal action
        }
        
        XCTAssertNotNil(tag)
    }
    
    // MARK: - Family Info Tests
    
    func testFamilyInfoViewCreation() {
        let familyView = FamilyInfoView()
            .environmentObject(profileService)
            .environmentObject(appPreferences)
        
        XCTAssertNotNil(familyView)
    }
    
    func testFamilyInfoCalculations() {
        let familyInfo = FamilyInfo(
            memberCount: 4,
            hasChildren: true,
            childrenAges: [8, 12],
            specialDietaryNeeds: ["gluten-free for child"],
            notes: "Test family"
        )
        
        XCTAssertEqual(familyInfo.memberCount, 4)
        XCTAssertTrue(familyInfo.hasChildren)
        XCTAssertEqual(familyInfo.childrenAges.count, 2)
        XCTAssertTrue(familyInfo.hasSchoolAgeChildren)
        XCTAssertFalse(familyInfo.hasToddlers)
        XCTAssertGreaterThan(familyInfo.servingSizeMultiplier, 1.0)
    }
    
    func testChildAgeTagComponent() {
        let tag = ChildAgeTag(
            age: 8,
            fontScale: 1.0
        ) {
            // Test removal action
        }
        
        XCTAssertNotNil(tag)
    }
    
    // MARK: - Notification Settings Tests
    
    func testNotificationSettingsViewCreation() {
        let notificationView = NotificationSettingsView()
            .environmentObject(profileService)
            .environmentObject(appPreferences)
        
        XCTAssertNotNil(notificationView)
    }
    
    func testNotificationFrequencyEnums() {
        // Test ShoppingReminderFrequency
        let shoppingFreqs = ShoppingReminderFrequency.allCases
        XCTAssertEqual(shoppingFreqs.count, 3)
        
        for freq in shoppingFreqs {
            XCTAssertFalse(freq.displayName.isEmpty)
            XCTAssertFalse(freq.description.isEmpty)
        }
        
        // Test RecommendationFrequency
        let recFreqs = RecommendationFrequency.allCases
        XCTAssertEqual(recFreqs.count, 3)
        
        for freq in recFreqs {
            XCTAssertFalse(freq.displayName.isEmpty)
            XCTAssertFalse(freq.description.isEmpty)
        }
    }
    
    func testNotificationToggleComponent() {
        let toggle = NotificationToggleRow(
            title: "Test Notification",
            subtitle: "Test description",
            isOn: .constant(true),
            fontScale: 1.0
        ) { _ in
            // Test toggle action
        }
        
        XCTAssertNotNil(toggle)
    }
    
    // MARK: - App Preferences Tests
    
    func testAppPreferencesViewCreation() {
        let appPrefsView = AppPreferencesView()
            .environmentObject(profileService)
            .environmentObject(appPreferences)
        
        XCTAssertNotNil(appPrefsView)
    }
    
    func testFontSizeOptions() {
        let fontSizes = FontSize.allCases
        XCTAssertEqual(fontSizes.count, 4)
        
        for fontSize in fontSizes {
            XCTAssertFalse(fontSize.displayName.isEmpty)
            XCTAssertGreaterThan(fontSize.scaleFactor, 0.5)
            XCTAssertLessThan(fontSize.scaleFactor, 2.0)
        }
    }
    
    func testLanguageOptions() {
        let languages = Language.allCases
        XCTAssertGreaterThan(languages.count, 3)
        
        for language in languages {
            XCTAssertFalse(language.displayName.isEmpty)
            XCTAssertFalse(language.nativeName.isEmpty)
            XCTAssertFalse(language.sampleText.isEmpty)
        }
        
        // Test that English is available
        XCTAssertTrue(Language.english.isAvailable)
    }
    
    func testMeasurementUnitOptions() {
        let units = MeasurementUnit.allCases
        XCTAssertEqual(units.count, 3)
        
        for unit in units {
            XCTAssertFalse(unit.displayName.isEmpty)
            XCTAssertFalse(unit.description.isEmpty)
            XCTAssertFalse(unit.weightExample.isEmpty)
            XCTAssertFalse(unit.volumeExample.isEmpty)
            XCTAssertFalse(unit.temperatureExample.isEmpty)
        }
    }
    
    // MARK: - Data Privacy Tests
    
    func testDataPrivacyViewCreation() {
        let privacyView = DataPrivacyView()
            .environmentObject(profileService)
            .environmentObject(syncService)
            .environmentObject(authService)
            .environmentObject(appPreferences)
        
        XCTAssertNotNil(privacyView)
    }
    
    func testDataStatsCalculation() {
        let stats = DataStats(
            foodPreferencesCount: 5,
            familyMembersCount: 4,
            accountAgeDays: 45,
            estimatedDataSizeKB: 25
        )
        
        XCTAssertEqual(stats.foodPreferencesCount, 5)
        XCTAssertEqual(stats.familyMembersCount, 4)
        XCTAssertFalse(stats.accountAgeText.isEmpty)
        XCTAssertFalse(stats.dataSizeText.isEmpty)
    }
    
    func testDataStatCardComponent() {
        let card = DataStatCard(
            title: "Test Stat",
            value: "5",
            icon: "chart.bar.fill",
            color: .blue,
            fontScale: 1.0
        )
        
        XCTAssertNotNil(card)
    }
    
    // MARK: - Integration Tests
    
    func testCompleteUserFlow() async {
        // Test that a complete user flow can be simulated
        
        // 1. Create user profile
        var profile = UserProfile(id: "test-user")
        profile.strictExclusions = ["pork", "alcohol"]
        profile.allergies = ["peanuts", "shellfish"]
        profile.familyInfo = FamilyInfo(memberCount: 3, hasChildren: true, childrenAges: [6])
        
        // 2. Test profile validation
        XCTAssertEqual(profile.strictExclusions.count, 2)
        XCTAssertEqual(profile.allergies.count, 2)
        XCTAssertEqual(profile.familyInfo.memberCount, 3)
        XCTAssertTrue(profile.familyInfo.hasChildren)
        
        // 3. Test exclusion logic
        XCTAssertTrue(profile.shouldExcludeIngredient("pork"))
        XCTAssertTrue(profile.shouldExcludeIngredient("peanuts"))
        XCTAssertFalse(profile.shouldExcludeIngredient("chicken"))
        
        // 4. Test family calculations
        XCTAssertTrue(profile.familyInfo.hasToddlers)
        XCTAssertGreaterThan(profile.familyInfo.servingSizeMultiplier, 1.0)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityCompliance() {
        // Test font scaling
        let scales: [CGFloat] = [0.9, 1.0, 1.2, 1.4]
        
        for scale in scales {
            let appPrefs = AppPreferences()
            appPrefs.fontSize = FontSize.allCases.first { $0.scaleFactor == scale } ?? .medium
            
            XCTAssertNotNil(appPrefs)
            XCTAssertEqual(appPrefs.fontSize.scaleFactor, scale, accuracy: 0.1)
        }
    }
    
    func testUserPersonaSupport() {
        // Test Robert Jones (Tech Novice) support
        let largeFont = FontSize.extraLarge
        XCTAssertEqual(largeFont.scaleFactor, 1.4)
        
        // Test that all UI components support font scaling
        let fontScale = largeFont.scaleFactor
        XCTAssertGreaterThan(fontScale, 1.0)
        
        // Test that colors are accessible
        let colors: [Color] = [.red, .green, .blue, .orange, .purple]
        for color in colors {
            XCTAssertNotNil(color)
        }
    }
    
    // MARK: - Performance Tests
    
    func testViewCreationPerformance() {
        measure {
            for _ in 0..<50 {
                let _ = UserProfileView()
                    .environmentObject(authService)
                    .environmentObject(profileService)
                    .environmentObject(syncService)
                    .environmentObject(appPreferences)
            }
        }
    }
    
    func testPreferenceViewsPerformance() {
        measure {
            let _ = FoodPreferencesView()
                .environmentObject(profileService)
                .environmentObject(appPreferences)
            
            let _ = FamilyInfoView()
                .environmentObject(profileService)
                .environmentObject(appPreferences)
            
            let _ = NotificationSettingsView()
                .environmentObject(profileService)
                .environmentObject(appPreferences)
            
            let _ = AppPreferencesView()
                .environmentObject(profileService)
                .environmentObject(appPreferences)
            
            let _ = DataPrivacyView()
                .environmentObject(profileService)
                .environmentObject(syncService)
                .environmentObject(authService)
                .environmentObject(appPreferences)
        }
    }
}
