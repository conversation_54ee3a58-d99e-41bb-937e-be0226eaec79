import XCTest
import <PERSON><PERSON>
@testable import IngredientScanner

/// Comprehensive end-to-end integration tests for the complete user management system
/// 
/// Tests the full user journey from unauthenticated state through authentication
/// to profile management, ensuring all components work together seamlessly.
/// 
/// Test Coverage:
/// - Complete authentication flow
/// - Profile tab integration
/// - Service container integration
/// - Data synchronization
/// - UI state management
/// - Error handling across the system
@MainActor
final class EndToEndIntegrationTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var serviceContainer: ServiceContainer!
    private var appCoordinator: AppCoordinator!
    
    // MARK: - Setup & Teardown
    
    override func setUp() async throws {
        try await super.setUp()
        
        // Initialize the complete system
        serviceContainer = ServiceContainer.shared
        appCoordinator = AppCoordinator(
            pantryService: serviceContainer.pantryService,
            shoppingListService: serviceContainer.shoppingListService
        )
        
        // Wait for initialization
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    override func tearDown() async throws {
        serviceContainer = nil
        appCoordinator = nil
        
        try await super.tearDown()
    }
    
    // MARK: - System Integration Tests
    
    func testServiceContainerIntegration() {
        // Test that ServiceContainer properly integrates UserManagementModule
        XCTAssertNotNil(serviceContainer)
        XCTAssertNotNil(serviceContainer.userProfileService)
        XCTAssertNotNil(serviceContainer.authenticationService)
        XCTAssertNotNil(serviceContainer.userDataSyncService)
        
        // Test that existing services are still available
        XCTAssertNotNil(serviceContainer.pantryService)
        XCTAssertNotNil(serviceContainer.shoppingListService)
        XCTAssertNotNil(serviceContainer.googleVisionService)
        XCTAssertNotNil(serviceContainer.geminiService)
        XCTAssertNotNil(serviceContainer.recipeGenerationService)
    }
    
    func testAppCoordinatorIntegration() {
        // Test that AppCoordinator supports Profile tab
        XCTAssertNotNil(appCoordinator)
        XCTAssertEqual(appCoordinator.selectedTab, 0) // Default to Scanner
        
        // Test tab switching
        appCoordinator.switchToProfileTab()
        XCTAssertEqual(appCoordinator.selectedTab, 4)
        
        appCoordinator.switchToPantryTab()
        XCTAssertEqual(appCoordinator.selectedTab, 1)
    }
    
    func testUserManagementModuleConfiguration() {
        // Test that UserManagementModule is properly configured
        XCTAssertTrue(UserManagementModule.shared.isUserAuthenticated == false) // Initial state
        
        // Test view creation
        let profileView = UserManagementModule.shared.createUserProfileView()
        XCTAssertNotNil(profileView)
        
        let loginView = UserManagementModule.shared.createLoginView()
        XCTAssertNotNil(loginView)
        
        let authContainerView = UserManagementModule.shared.createAuthenticationContainerView()
        XCTAssertNotNil(authContainerView)
    }
    
    // MARK: - Authentication Flow Tests
    
    func testCompleteAuthenticationFlow() async {
        let authService = serviceContainer.authenticationService
        let profileService = serviceContainer.userProfileService
        let syncService = serviceContainer.userDataSyncService
        
        // Initial state
        XCTAssertFalse(authService.isAuthenticated)
        XCTAssertNil(authService.currentUser)
        XCTAssertNil(profileService.userProfile)
        XCTAssertEqual(syncService.syncState, .idle)
        
        // Test authentication attempt (will fail without real Firebase)
        await authService.signInWithEmail("<EMAIL>", password: "wrongpassword")
        
        // Should result in error state
        XCTAssertFalse(authService.isAuthenticated)
        XCTAssertNotNil(authService.currentError)
    }
    
    func testDataSynchronizationFlow() async {
        let syncService = serviceContainer.userDataSyncService
        
        // Test manual sync without authentication
        await syncService.forceSyncUserData()
        
        // Should handle gracefully
        XCTAssertEqual(syncService.syncState, .idle)
        
        // Test data clearing
        await syncService.clearLocalUserData()
        XCTAssertEqual(syncService.syncState, .idle)
        XCTAssertNil(syncService.lastSyncDate)
    }
    
    // MARK: - UI Integration Tests
    
    func testUserProfileViewIntegration() throws {
        // Test UserProfileView creation with all dependencies
        let profileView = UserProfileView()
            .environmentObject(serviceContainer.authenticationService)
            .environmentObject(serviceContainer.userProfileService)
            .environmentObject(serviceContainer.userDataSyncService)
            .environmentObject(AppPreferences())
        
        XCTAssertNotNil(profileView)
    }
    
    func testTabViewIntegration() throws {
        // Test that the complete tab view can be created
        let tabView = appCoordinator.rootView()
        XCTAssertNotNil(tabView)
    }
    
    // MARK: - Error Handling Tests
    
    func testSystemErrorHandling() async {
        let authService = serviceContainer.authenticationService
        
        // Test invalid email authentication
        await authService.signInWithEmail("invalid-email", password: "password")
        
        // Should handle error gracefully
        XCTAssertNotNil(authService.currentError)
        XCTAssertFalse(authService.isAuthenticated)
        XCTAssertFalse(authService.isLoading)
    }
    
    func testServiceErrorPropagation() async {
        let syncService = serviceContainer.userDataSyncService
        
        // Test error handling in sync service
        // This would normally test actual Firebase errors
        XCTAssertNotNil(syncService)
        XCTAssertEqual(syncService.syncState, .idle)
    }
    
    // MARK: - Performance Tests
    
    func testSystemInitializationPerformance() {
        measure {
            // Test ServiceContainer initialization performance
            let container = ServiceContainer.shared
            _ = container.authenticationService
            _ = container.userProfileService
            _ = container.userDataSyncService
        }
    }
    
    func testViewCreationPerformance() {
        measure {
            // Test view creation performance
            for _ in 0..<100 {
                let _ = UserManagementModule.shared.createUserProfileView()
            }
        }
    }
    
    // MARK: - Memory Management Tests
    
    func testMemoryLeaks() async {
        weak var weakAuthService: AuthenticationService?
        weak var weakProfileService: UserProfileService?
        weak var weakSyncService: UserDataSyncService?
        
        autoreleasepool {
            let authService = AuthenticationService()
            let profileService = UserProfileService()
            let syncService = UserDataSyncService(
                authService: authService,
                profileService: profileService
            )
            
            weakAuthService = authService
            weakProfileService = profileService
            weakSyncService = syncService
            
            // Use services briefly
            _ = authService.isAuthenticated
            _ = profileService.hasProfile
            _ = syncService.isSyncing
        }
        
        // Wait for deallocation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        // Services should be deallocated (this test may not work with singletons)
        // XCTAssertNil(weakAuthService)
        // XCTAssertNil(weakProfileService)
        // XCTAssertNil(weakSyncService)
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityCompliance() {
        // Test that all views support accessibility
        let profileView = UserProfileView()
            .environmentObject(serviceContainer.authenticationService)
            .environmentObject(serviceContainer.userProfileService)
            .environmentObject(serviceContainer.userDataSyncService)
            .environmentObject(AppPreferences())
        
        // This would require more sophisticated accessibility testing
        XCTAssertNotNil(profileView)
    }
    
    // MARK: - User Persona Tests
    
    func testRobertJonesUserExperience() {
        // Test large font support
        let appPreferences = AppPreferences(fontSize: .extraLarge)
        XCTAssertEqual(appPreferences.fontSize.scaleFactor, 1.4)
        
        // Test that UI scales properly
        let fontScale = appPreferences.fontSize.scaleFactor
        XCTAssertGreaterThan(fontScale, 1.0)
    }
    
    func testChloeAllergySupportExperience() {
        // Test allergy management
        var profile = UserProfile(id: "test")
        profile.allergies = ["peanuts", "shellfish"]
        profile.strictExclusions = ["dairy"]
        
        XCTAssertTrue(profile.shouldExcludeIngredient("peanuts"))
        XCTAssertTrue(profile.shouldExcludeIngredient("shellfish"))
        XCTAssertTrue(profile.shouldExcludeIngredient("dairy"))
        XCTAssertFalse(profile.shouldExcludeIngredient("tomatoes"))
    }
    
    func testDavidFamilyManagementExperience() {
        // Test family information management
        let familyInfo = FamilyInfo(
            memberCount: 4,
            hasChildren: true,
            childrenAges: [8, 12]
        )
        
        XCTAssertEqual(familyInfo.memberCount, 4)
        XCTAssertTrue(familyInfo.hasChildren)
        XCTAssertTrue(familyInfo.hasSchoolAgeChildren)
        XCTAssertFalse(familyInfo.hasToddlers)
        XCTAssertGreaterThan(familyInfo.servingSizeMultiplier, 1.0)
    }
    
    // MARK: - Integration Stress Tests
    
    func testConcurrentServiceAccess() async {
        // Test concurrent access to services
        let tasks = (0..<10).map { index in
            Task {
                let authService = serviceContainer.authenticationService
                let profileService = serviceContainer.userProfileService
                
                // Simulate concurrent access
                let isAuth = authService.isAuthenticated
                let hasProfile = profileService.hasProfile
                
                return (isAuth, hasProfile, index)
            }
        }
        
        let results = await withTaskGroup(of: (Bool, Bool, Int).self) { group in
            for task in tasks {
                group.addTask {
                    await task.value
                }
            }
            
            var allResults: [(Bool, Bool, Int)] = []
            for await result in group {
                allResults.append(result)
            }
            return allResults
        }
        
        XCTAssertEqual(results.count, 10)
    }
    
    func testRapidTabSwitching() {
        // Test rapid tab switching performance
        measure {
            for i in 0..<100 {
                appCoordinator.selectedTab = i % 5
            }
        }
    }
}
