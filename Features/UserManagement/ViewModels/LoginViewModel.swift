import Foundation
import Combine

/// ViewModel for login and authentication flows
/// 
/// Handles the business logic for user authentication, form validation,
/// and state management for login and signup processes.
/// 
/// Features:
/// - Real-time form validation
/// - Authentication state management
/// - Error handling and recovery
/// - Accessibility support
/// - User preference integration
@MainActor
class LoginViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    // Authentication State
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var authenticationError: AuthenticationError?
    @Published var isLoading = false
    
    // Login Form State
    @Published var loginEmail = ""
    @Published var loginPassword = ""
    @Published var loginEmailError: String?
    @Published var loginPasswordError: String?
    
    // Signup Form State
    @Published var signupEmail = ""
    @Published var signupPassword = ""
    @Published var signupConfirmPassword = ""
    @Published var signupDisplayName = ""
    @Published var signupEmailError: String?
    @Published var signupPasswordError: String?
    @Published var signupConfirmPasswordError: String?
    @Published var signupDisplayNameError: String?
    @Published var agreedToTerms = false
    
    // UI State
    @Published var showingSignUp = false
    @Published var showingLoginPassword = false
    @Published var showingSignupPassword = false
    @Published var showingSignupConfirmPassword = false
    
    // MARK: - Dependencies
    
    private let authService: AuthenticationService
    private let logger = LoginLogger()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(authService: AuthenticationService) {
        self.authService = authService
        setupBindings()
        logger.log("LoginViewModel initialized")
    }
    
    // MARK: - Computed Properties
    
    /// Returns true if login form is valid
    var isLoginFormValid: Bool {
        !loginEmail.isEmpty && !loginPassword.isEmpty &&
        loginEmailError == nil && loginPasswordError == nil
    }
    
    /// Returns true if signup form is valid
    var isSignupFormValid: Bool {
        !signupEmail.isEmpty && !signupPassword.isEmpty && !signupConfirmPassword.isEmpty &&
        signupEmailError == nil && signupPasswordError == nil && signupConfirmPasswordError == nil &&
        agreedToTerms
    }
    
    /// Returns password strength for signup
    var signupPasswordStrength: PasswordStrength {
        PasswordStrength.evaluate(signupPassword)
    }
    
    // MARK: - Authentication Actions
    
    /// Sign in with Apple
    func signInWithApple() async {
        logger.log("Attempting Apple Sign-In")
        setLoading(true)
        clearErrors()
        
        await authService.signInWithApple()
        setLoading(false)
    }
    
    /// Sign in with Google
    func signInWithGoogle() async {
        logger.log("Attempting Google Sign-In")
        setLoading(true)
        clearErrors()
        
        await authService.signInWithGoogle()
        setLoading(false)
    }
    
    /// Sign in with email and password
    func signInWithEmail() async {
        logger.log("Attempting email Sign-In")
        
        // Validate form before proceeding
        validateLoginForm()
        guard isLoginFormValid else {
            logger.log("Login form validation failed")
            return
        }
        
        setLoading(true)
        clearErrors()
        
        await authService.signInWithEmail(loginEmail, password: loginPassword)
        setLoading(false)
    }
    
    /// Create new account
    func createAccount() async {
        logger.log("Attempting account creation")
        
        // Validate form before proceeding
        validateSignupForm()
        guard isSignupFormValid else {
            logger.log("Signup form validation failed")
            return
        }
        
        setLoading(true)
        clearErrors()
        
        let displayName = signupDisplayName.isEmpty ? nil : signupDisplayName
        await authService.createAccount(
            email: signupEmail,
            password: signupPassword,
            displayName: displayName
        )
        setLoading(false)
    }
    
    /// Sign out current user
    func signOut() async {
        logger.log("Signing out user")
        setLoading(true)
        
        await authService.signOut()
        
        // Clear form data on sign out
        clearFormData()
        setLoading(false)
    }
    
    // MARK: - Form Validation
    
    /// Validate login form
    func validateLoginForm() {
        validateLoginEmail()
        validateLoginPassword()
    }
    
    /// Validate signup form
    func validateSignupForm() {
        validateSignupEmail()
        validateSignupPassword()
        validateSignupConfirmPassword()
        validateSignupDisplayName()
    }
    
    /// Validate login email
    func validateLoginEmail() {
        loginEmailError = validateEmail(loginEmail)
    }
    
    /// Validate login password
    func validateLoginPassword() {
        loginPasswordError = validatePassword(loginPassword, isSignup: false)
    }
    
    /// Validate signup email
    func validateSignupEmail() {
        signupEmailError = validateEmail(signupEmail)
    }
    
    /// Validate signup password
    func validateSignupPassword() {
        signupPasswordError = validatePassword(signupPassword, isSignup: true)
        // Re-validate confirm password if it's already filled
        if !signupConfirmPassword.isEmpty {
            validateSignupConfirmPassword()
        }
    }
    
    /// Validate signup confirm password
    func validateSignupConfirmPassword() {
        signupConfirmPasswordError = nil
        
        guard !signupConfirmPassword.isEmpty else { return }
        
        if signupPassword != signupConfirmPassword {
            signupConfirmPasswordError = "Passwords do not match"
        }
    }
    
    /// Validate signup display name
    func validateSignupDisplayName() {
        signupDisplayNameError = nil
        
        guard !signupDisplayName.isEmpty else { return }
        
        if signupDisplayName.count > 50 {
            signupDisplayNameError = "Display name must be 50 characters or less"
        }
    }
    
    // MARK: - UI Actions
    
    /// Toggle signup view
    func toggleSignUp() {
        showingSignUp.toggle()
        if showingSignUp {
            clearLoginForm()
        } else {
            clearSignupForm()
        }
    }
    
    /// Toggle login password visibility
    func toggleLoginPasswordVisibility() {
        showingLoginPassword.toggle()
    }
    
    /// Toggle signup password visibility
    func toggleSignupPasswordVisibility() {
        showingSignupPassword.toggle()
    }
    
    /// Toggle signup confirm password visibility
    func toggleSignupConfirmPasswordVisibility() {
        showingSignupConfirmPassword.toggle()
    }
    
    /// Toggle terms agreement
    func toggleTermsAgreement() {
        agreedToTerms.toggle()
    }
}

// MARK: - Private Implementation

private extension LoginViewModel {

    /// Setup reactive bindings
    func setupBindings() {
        // Bind authentication service state
        authService.$authenticationState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleAuthenticationStateChange(state)
            }
            .store(in: &cancellables)

        authService.$isLoading
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)

        // Setup real-time validation
        setupRealtimeValidation()
    }

    /// Setup real-time form validation
    func setupRealtimeValidation() {
        // Login form validation
        $loginEmail
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.validateLoginEmail()
            }
            .store(in: &cancellables)

        $loginPassword
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.validateLoginPassword()
            }
            .store(in: &cancellables)

        // Signup form validation
        $signupEmail
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.validateSignupEmail()
            }
            .store(in: &cancellables)

        $signupPassword
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.validateSignupPassword()
            }
            .store(in: &cancellables)

        $signupConfirmPassword
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.validateSignupConfirmPassword()
            }
            .store(in: &cancellables)

        $signupDisplayName
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.validateSignupDisplayName()
            }
            .store(in: &cancellables)
    }

    /// Handle authentication state changes
    func handleAuthenticationStateChange(_ state: AuthenticationState) {
        switch state {
        case .authenticated(let user):
            isAuthenticated = true
            currentUser = user
            authenticationError = nil
            logger.log("User authenticated: \(user.id)")

        case .unauthenticated:
            isAuthenticated = false
            currentUser = nil
            authenticationError = nil
            logger.log("User unauthenticated")

        case .loading:
            // Loading state is handled by isLoading binding
            break

        case .error(let error):
            isAuthenticated = false
            currentUser = nil
            authenticationError = error
            logger.logError("Authentication error", error: error)
        }
    }

    /// Set loading state
    func setLoading(_ loading: Bool) {
        isLoading = loading
    }

    /// Clear all errors
    func clearErrors() {
        authenticationError = nil
        loginEmailError = nil
        loginPasswordError = nil
        signupEmailError = nil
        signupPasswordError = nil
        signupConfirmPasswordError = nil
        signupDisplayNameError = nil
    }

    /// Clear all form data
    func clearFormData() {
        clearLoginForm()
        clearSignupForm()
    }

    /// Clear login form
    func clearLoginForm() {
        loginEmail = ""
        loginPassword = ""
        loginEmailError = nil
        loginPasswordError = nil
        showingLoginPassword = false
    }

    /// Clear signup form
    func clearSignupForm() {
        signupEmail = ""
        signupPassword = ""
        signupConfirmPassword = ""
        signupDisplayName = ""
        signupEmailError = nil
        signupPasswordError = nil
        signupConfirmPasswordError = nil
        signupDisplayNameError = nil
        agreedToTerms = false
        showingSignupPassword = false
        showingSignupConfirmPassword = false
    }

    /// Validate email format
    func validateEmail(_ email: String) -> String? {
        guard !email.isEmpty else { return nil }

        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)

        if !emailPredicate.evaluate(with: email) {
            return "Please enter a valid email address"
        }

        return nil
    }

    /// Validate password
    func validatePassword(_ password: String, isSignup: Bool) -> String? {
        guard !password.isEmpty else { return nil }

        let minLength = isSignup ? 8 : 6

        if password.count < minLength {
            return "Password must be at least \(minLength) characters"
        }

        if isSignup && PasswordStrength.evaluate(password) == .weak {
            return "Password is too weak. Add numbers, symbols, or mix case."
        }

        return nil
    }
}

// MARK: - Login Logger

private struct LoginLogger {

    func log(_ message: String) {
        print("🔐 [LoginVM] \(message)")
    }

    func logError(_ message: String, error: Error) {
        print("❌ [LoginVM] \(message): \(error.localizedDescription)")
    }
}
