# User Management Module - Integration Guide

## 🚀 快速集成指南

### 第1步: 模块配置

在应用启动时配置模块：

```swift
// Application/App.swift
import SwiftUI

@main
struct IngredientScannerApp: App {
    
    init() {
        // 配置Firebase
        FirebaseApp.configure()
        
        // 配置用户管理模块
        UserManagementModule.shared.configure()
    }
    
    var body: some Scene {
        WindowGroup {
            // 应用主视图
        }
    }
}
```

### 第2步: 集成到ServiceContainer

```swift
// Services/ServiceContainer.swift
@MainActor
class ServiceContainer: ObservableObject {
    static let shared = ServiceContainer()
    
    // 现有服务
    let pantryService = PantryService()
    let shoppingListService = ShoppingListService()
    
    // 用户管理服务
    var userProfileService: UserProfileService {
        UserManagementModule.shared.userProfileService
    }
    
    var authenticationService: AuthenticationService {
        UserManagementModule.shared.authenticationService
    }
    
    // 环境对象注入
    func environmentObjects<Content: View>(_ content: Content) -> some View {
        UserManagementModule.shared.environmentObjects(
            content
                .environmentObject(pantryService)
                .environmentObject(shoppingListService)
        )
    }
}
```

### 第3步: 添加Profile Tab

```swift
// Coordinator/AppCoordinator.swift
struct RootTabView: View {
    @ObservedObject var coordinator: AppCoordinator
    
    var body: some View {
        TabView(selection: $coordinator.selectedTab) {
            // 现有的4个Tab...
            
            // 新增: Profile Tab
            UserManagementModule.shared.createUserProfileView()
                .tabItem {
                    Label("Profile", systemImage: "person.circle")
                }
                .tag(4)
        }
        .environmentObject(ServiceContainer.shared.pantryService)
        .environmentObject(ServiceContainer.shared.shoppingListService)
    }
}
```

## 🔌 模块接口使用

### 检查登录状态

```swift
if UserManagementModule.shared.isUserAuthenticated {
    // 用户已登录
    let userProfile = UserManagementModule.shared.currentUserProfile
} else {
    // 显示登录界面
    let loginView = UserManagementModule.shared.createLoginView()
}
```

### 获取用户偏好

```swift
// 在RecipeGeneratorViewModel中
class RecipeGeneratorViewModel: ObservableObject {
    
    func generateMealIdeas() async {
        let userProfile = UserManagementModule.shared.currentUserProfile
        
        let preferences = RecipePreferences(
            cookingTimeInMinutes: cookingTimeInMinutes,
            numberOfServings: numberOfServings,
            dietaryRestrictions: selectedRestrictions,
            // 集成用户偏好
            strictExclusions: userProfile?.strictExclusions ?? [],
            allergies: userProfile?.allergies ?? []
        )
        
        // 生成食谱...
    }
}
```

## 📋 集成检查清单

### ✅ 必需步骤
- [ ] 在App.swift中配置模块
- [ ] 更新ServiceContainer集成用户服务
- [ ] 在AppCoordinator中添加Profile Tab
- [ ] 配置Firebase Authentication
- [ ] 测试登录流程

### ✅ 可选步骤
- [ ] 自定义UI主题
- [ ] 添加额外的认证方式
- [ ] 配置推送通知
- [ ] 设置数据分析

## 🧪 测试验证

### 功能测试
1. 用户可以成功登录/注册
2. 用户偏好可以保存和加载
3. 登出功能正常工作
4. 数据在设备间同步

### 集成测试
1. Profile Tab正确显示
2. 用户偏好影响食谱生成
3. 认证状态正确传播
4. 错误处理正常工作

## 🔧 故障排除

### 常见问题

**问题**: 模块未正确初始化
**解决**: 确保在App.swift中调用了`UserManagementModule.shared.configure()`

**问题**: Firebase认证失败
**解决**: 检查GoogleService-Info.plist配置和API密钥

**问题**: 用户偏好未生效
**解决**: 确认ServiceContainer正确集成了用户服务

## 📞 支持

如果遇到集成问题，请检查：
1. 模块是否正确配置
2. Firebase设置是否完整
3. 依赖关系是否正确建立
4. 错误日志中的具体信息
