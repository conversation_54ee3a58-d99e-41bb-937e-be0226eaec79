import SwiftUI
import Combine
import FirebaseCore

/// 用户管理模块的主入口点
/// 提供标准化的模块集成接口，确保与主应用的无缝集成
@MainActor
public class UserManagementModule: ObservableObject {
    
    // MARK: - Singleton
    public static let shared = UserManagementModule()
    
    // MARK: - Services
    /// 用户资料服务 - 处理用户数据的CRUD操作
    public private(set) var userProfileService: UserProfileService!
    
    /// 认证服务 - 处理用户登录、注册、登出
    public private(set) var authenticationService: AuthenticationService!
    
    /// 数据同步服务 - 处理与Firebase的数据同步
    public private(set) var userDataSyncService: UserDataSyncService!
    
    // MARK: - Configuration
    private var isConfigured = false
    
    private init() {
        // Private initializer to enforce singleton pattern
    }
    
    // MARK: - Public Interface
    
    /// 配置用户管理模块
    /// 必须在使用模块前调用此方法
    public func configure() {
        guard !isConfigured else {
            print("⚠️ UserManagementModule already configured")
            return
        }

        // 配置Firebase
        FirebaseApp.configure()

        // 初始化服务
        initializeServices()

        // 设置服务间的依赖关系
        setupServiceDependencies()

        isConfigured = true
        print("✅ UserManagementModule configured successfully")
    }
    
    /// 获取用户资料主视图
    /// - Returns: 用户资料的SwiftUI视图
    public func createUserProfileView() -> some View {
        guard isConfigured else {
            fatalError("UserManagementModule must be configured before creating views")
        }

        return UserProfileView()
            .environmentObject(userProfileService)
            .environmentObject(authenticationService)
            .environmentObject(userDataSyncService)
            .environmentObject(AppPreferences())
    }
    
    /// 获取登录视图
    /// - Returns: 登录的SwiftUI视图
    public func createLoginView() -> some View {
        guard isConfigured else {
            fatalError("UserManagementModule must be configured before creating views")
        }

        return LoginView()
            .environmentObject(authenticationService)
            .environmentObject(AppPreferences()) // Add app preferences support
    }

    /// 获取邮箱输入视图
    /// - Returns: 邮箱输入的SwiftUI视图
    public func createEmailInputView() -> some View {
        guard isConfigured else {
            fatalError("UserManagementModule must be configured before creating views")
        }

        return EmailInputView()
            .environmentObject(authenticationService)
    }

    /// 获取完整的认证容器视图
    /// - Returns: 认证容器的SwiftUI视图
    public func createAuthenticationContainerView() -> some View {
        guard isConfigured else {
            fatalError("UserManagementModule must be configured before creating views")
        }

        return AuthenticationContainerView()
            .environmentObject(authenticationService)
            .environmentObject(userDataSyncService)
            .environmentObject(AppPreferences())
    }
    
    /// 检查用户是否已登录
    /// - Returns: 登录状态
    public var isUserAuthenticated: Bool {
        guard isConfigured else { return false }
        return authenticationService.isAuthenticated
    }
    
    /// 获取当前用户资料
    /// - Returns: 用户资料对象，如果未登录则返回nil
    public var currentUserProfile: UserProfile? {
        guard isConfigured else { return nil }
        return userProfileService.userProfile
    }
    
    // MARK: - Private Methods
    
    private func initializeServices() {
        // Initialize core services
        authenticationService = AuthenticationService()
        userProfileService = UserProfileService()
        userDataSyncService = UserDataSyncService(
            authService: authenticationService,
            profileService: userProfileService
        )

        print("🔧 Services initialized successfully")
    }

    private func setupServiceDependencies() {
        // Services are already properly connected through dependency injection
        // UserDataSyncService coordinates between auth and profile services

        print("🔗 Service dependencies configured successfully")
    }
}

// MARK: - Integration Helpers

extension UserManagementModule {
    
    /// 为SwiftUI视图注入用户管理相关的环境对象
    /// - Parameter content: 要注入环境对象的视图
    /// - Returns: 注入了环境对象的视图
    public func environmentObjects<Content: View>(_ content: Content) -> some View {
        guard isConfigured else {
            fatalError("UserManagementModule must be configured before injecting environment objects")
        }

        return content
            .environmentObject(userProfileService)
            .environmentObject(authenticationService)
            .environmentObject(userDataSyncService)
    }
}

// MARK: - Module Information

extension UserManagementModule {
    
    /// 模块版本信息
    public static let version = "1.0.0"
    
    /// 模块名称
    public static let moduleName = "UserManagement"
    
    /// 模块描述
    public static let description = "Complete user management solution with authentication, preferences, and profile management"
    
    /// 获取模块信息
    /// - Returns: 包含模块信息的字典
    public static func getModuleInfo() -> [String: Any] {
        return [
            "name": moduleName,
            "version": version,
            "description": description,
            "features": [
                "Authentication (Apple, Google, Email)",
                "User Profile Management",
                "Food Preferences & Restrictions",
                "Family Information",
                "Notification Settings",
                "App Preferences"
            ]
        ]
    }
}
