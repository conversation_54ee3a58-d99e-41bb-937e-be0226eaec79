import Foundation
import FirebaseCore
import FirebaseAuth
import AuthenticationServices
import GoogleSignIn
import Combine

/// Firebase authentication service handling all sign-in methods
/// 
/// This service manages user authentication using Firebase Auth with support for:
/// - Apple Sign-In
/// - Google Sign-In  
/// - Email/Password authentication
/// 
/// Uses @MainActor to ensure all UI updates happen on the main thread
/// and follows Swift 6.0 concurrency guidelines.
@MainActor
class AuthenticationService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published private(set) var authenticationState: AuthenticationState = .loading
    @Published private(set) var isLoading = false
    
    // MARK: - Private Properties
    
    private var authStateListener: AuthStateDidChangeListenerHandle?
    private let logger = AuthenticationLogger()
    
    // MARK: - Initialization
    
    init() {
        // 🔥 CRITICAL: Configure Firebase first if not already configured
        if FirebaseApp.app() == nil {
            FirebaseApp.configure()
            logger.log("Firebase configured by AuthenticationService")
        } else {
            logger.log("Firebase already configured")
        }

        setupAuthStateListener()
        logger.log("AuthenticationService initialized")
    }
    
    deinit {
        if let listener = authStateListener {
            Auth.auth().removeStateDidChangeListener(listener)
        }
        logger.log("AuthenticationService deinitialized")
    }
    
    // MARK: - Public Authentication Methods
    
    /// Sign in with Apple using AuthenticationServices
    func signInWithApple() async {
        logger.log("Starting Apple Sign-In")
        setLoading(true)
        
        do {
            let appleIDCredential = try await performAppleSignIn()
            let firebaseCredential = try createFirebaseCredential(from: appleIDCredential)
            let authResult = try await Auth.auth().signIn(with: firebaseCredential)
            
            logger.log("Apple Sign-In successful for user: \(authResult.user.uid)")
            // State will be updated automatically by auth state listener
            
        } catch {
            logger.logError("Apple Sign-In failed", error: error)
            await handleAuthenticationError(error)
        }
        
        setLoading(false)
    }
    
    /// Sign in with Google using GoogleSignIn SDK
    func signInWithGoogle() async {
        logger.log("Starting Google Sign-In")
        setLoading(true)
        
        do {
            guard let windowScene = await getWindowScene() else {
                throw AuthenticationError.unknown("No window scene available")
            }
            
            let googleUser = try await performGoogleSignIn(windowScene: windowScene)
            let firebaseCredential = try createFirebaseCredential(from: googleUser)
            let authResult = try await Auth.auth().signIn(with: firebaseCredential)
            
            logger.log("Google Sign-In successful for user: \(authResult.user.uid)")
            // State will be updated automatically by auth state listener
            
        } catch {
            logger.logError("Google Sign-In failed", error: error)
            await handleAuthenticationError(error)
        }
        
        setLoading(false)
    }
    
    /// Sign in with email and password
    func signInWithEmail(_ email: String, password: String) async {
        logger.log("Starting email Sign-In for: \(email)")
        setLoading(true)
        
        do {
            let authResult = try await Auth.auth().signIn(withEmail: email, password: password)
            logger.log("Email Sign-In successful for user: \(authResult.user.uid)")
            // State will be updated automatically by auth state listener
            
        } catch {
            logger.logError("Email Sign-In failed", error: error)
            await handleAuthenticationError(error)
        }
        
        setLoading(false)
    }
    
    /// Create new account with email and password
    func createAccount(email: String, password: String, displayName: String?) async {
        logger.log("Creating account for: \(email)")
        setLoading(true)
        
        do {
            let authResult = try await Auth.auth().createUser(withEmail: email, password: password)
            
            // Update display name if provided
            if let displayName = displayName, !displayName.isEmpty {
                let changeRequest = authResult.user.createProfileChangeRequest()
                changeRequest.displayName = displayName
                try await changeRequest.commitChanges()
            }
            
            logger.log("Account created successfully for user: \(authResult.user.uid)")
            // State will be updated automatically by auth state listener
            
        } catch {
            logger.logError("Account creation failed", error: error)
            await handleAuthenticationError(error)
        }
        
        setLoading(false)
    }
    
    /// Sign out the current user
    func signOut() async {
        logger.log("Signing out user")
        setLoading(true)
        
        do {
            try Auth.auth().signOut()
            logger.log("Sign out successful")
            // State will be updated automatically by auth state listener
            
        } catch {
            logger.logError("Sign out failed", error: error)
            await handleAuthenticationError(error)
        }
        
        setLoading(false)
    }

    /// Check if an email address is already registered
    func checkEmailExists(_ email: String) async throws -> Bool {
        logger.log("Checking if email exists: \(email)")

        do {
            let signInMethods = try await Auth.auth().fetchSignInMethods(forEmail: email)
            let exists = !signInMethods.isEmpty

            logger.log("Email \(email) exists: \(exists)")
            return exists

        } catch {
            logger.logError("Failed to check email existence", error: error)
            throw error
        }
    }

    /// Developer sign-in for testing purposes
    /// Creates a mock authenticated user without requiring actual authentication
    func signInAsDeveloper() async {
        logger.log("Starting Developer Sign-In")
        setLoading(true)

        do {
            // Create a mock developer user with Firebase Auth
            let authResult = try await Auth.auth().signInAnonymously()

            // Update the user's display name to indicate it's a developer account
            let changeRequest = authResult.user.createProfileChangeRequest()
            changeRequest.displayName = "Developer User"
            try await changeRequest.commitChanges()

            logger.log("Developer Sign-In successful for user: \(authResult.user.uid)")
            // State will be updated automatically by auth state listener

        } catch {
            logger.logError("Developer Sign-In failed", error: error)
            await handleAuthenticationError(error)
        }

        setLoading(false)
    }

    /// Delete the current user account
    func deleteAccount() async {
        logger.log("Deleting user account")
        setLoading(true)
        
        do {
            guard let currentUser = Auth.auth().currentUser else {
                throw AuthenticationError.userNotFound
            }
            
            try await currentUser.delete()
            logger.log("Account deleted successfully")
            // State will be updated automatically by auth state listener
            
        } catch {
            logger.logError("Account deletion failed", error: error)
            await handleAuthenticationError(error)
        }
        
        setLoading(false)
    }
    
    // MARK: - Computed Properties
    
    /// Returns the current authenticated user, if any
    var currentUser: User? {
        authenticationState.user
    }
    
    /// Returns true if user is authenticated
    var isAuthenticated: Bool {
        authenticationState.isAuthenticated
    }
    
    /// Returns the current authentication error, if any
    var currentError: AuthenticationError? {
        authenticationState.error
    }
}

// MARK: - Private Implementation

private extension AuthenticationService {

    /// Setup Firebase auth state listener
    func setupAuthStateListener() {
        authStateListener = Auth.auth().addStateDidChangeListener { [weak self] _, user in
            Task { @MainActor in
                self?.updateAuthenticationState(with: user)
            }
        }
    }

    /// Update authentication state based on Firebase user
    func updateAuthenticationState(with firebaseUser: FirebaseAuth.User?) {
        if let firebaseUser = firebaseUser {
            let user = User(from: firebaseUser)
            authenticationState = .authenticated(user)
            logger.log("User authenticated: \(user.id)")
        } else {
            authenticationState = .unauthenticated
            logger.log("User unauthenticated")
        }
    }

    /// Set loading state
    func setLoading(_ loading: Bool) {
        isLoading = loading
        if loading {
            authenticationState = .loading
        }
    }

    /// Handle authentication errors
    func handleAuthenticationError(_ error: Error) async {
        let authError = mapToAuthenticationError(error)
        authenticationState = .error(authError)
        logger.logError("Authentication error", error: authError)
    }

    /// Map various error types to AuthenticationError
    func mapToAuthenticationError(_ error: Error) -> AuthenticationError {
        if let authError = error as? AuthenticationError {
            return authError
        }

        if let authErrorCode = AuthErrorCode(rawValue: (error as NSError).code) {
            switch authErrorCode {
            case .networkError:
                return .networkError
            case .userNotFound:
                return .userNotFound
            case .invalidEmail:
                return .invalidEmail
            case .emailAlreadyInUse:
                return .emailAlreadyInUse
            case .weakPassword:
                return .weakPassword
            case .wrongPassword:
                return .invalidCredentials
            case .userDisabled:
                return .userDisabled
            case .tooManyRequests:
                return .tooManyRequests
            case .operationNotAllowed:
                return .operationNotAllowed
            case .requiresRecentLogin:
                return .requiresRecentLogin
            case .providerAlreadyLinked:
                return .providerAlreadyLinked
            case .noSuchProvider:
                return .noSuchProvider
            case .invalidUserToken:
                return .invalidUserToken
            case .networkRequestFailed:
                return .networkRequestFailed
            case .userTokenExpired:
                return .userTokenExpired
            default:
                return .unknown(error.localizedDescription)
            }
        }

        return .unknown(error.localizedDescription)
    }

    // MARK: - Apple Sign-In Implementation

    /// Perform Apple Sign-In using AuthenticationServices
    func performAppleSignIn() async throws -> ASAuthorizationAppleIDCredential {
        return try await withCheckedThrowingContinuation { continuation in
            let request = ASAuthorizationAppleIDProvider().createRequest()
            request.requestedScopes = [.fullName, .email]

            let controller = ASAuthorizationController(authorizationRequests: [request])
            let delegate = AppleSignInDelegate { result in
                continuation.resume(with: result)
            }

            controller.delegate = delegate
            controller.presentationContextProvider = delegate
            controller.performRequests()
        }
    }

    /// Create Firebase credential from Apple ID credential
    func createFirebaseCredential(from appleCredential: ASAuthorizationAppleIDCredential) throws -> AuthCredential {
        guard let idToken = appleCredential.identityToken,
              let idTokenString = String(data: idToken, encoding: .utf8) else {
            throw AuthenticationError.invalidCredentials
        }

        let nonce = appleCredential.authorizationCode.flatMap { String(data: $0, encoding: .utf8) }
        return OAuthProvider.credential(withProviderID: "apple.com", idToken: idTokenString, rawNonce: nonce)
    }

    // MARK: - Google Sign-In Implementation

    /// Get the current window scene for Google Sign-In
    func getWindowScene() async -> UIWindowScene? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            return nil
        }
        return windowScene
    }

    /// Perform Google Sign-In
    func performGoogleSignIn(windowScene: UIWindowScene) async throws -> GIDGoogleUser {
        guard let presentingViewController = windowScene.windows.first?.rootViewController else {
            throw AuthenticationError.unknown("No presenting view controller available")
        }

        return try await withCheckedThrowingContinuation { continuation in
            GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController) { result, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let result = result {
                    continuation.resume(returning: result.user)
                } else {
                    continuation.resume(throwing: AuthenticationError.unknown("Unknown Google Sign-In error"))
                }
            }
        }
    }

    /// Create Firebase credential from Google user
    func createFirebaseCredential(from googleUser: GIDGoogleUser) throws -> AuthCredential {
        guard let idToken = googleUser.idToken?.tokenString else {
            throw AuthenticationError.invalidCredentials
        }

        return GoogleAuthProvider.credential(withIDToken: idToken, accessToken: googleUser.accessToken.tokenString)
    }
}

// MARK: - Apple Sign-In Delegate

private class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {

    private let completion: (Result<ASAuthorizationAppleIDCredential, Error>) -> Void

    init(completion: @escaping (Result<ASAuthorizationAppleIDCredential, Error>) -> Void) {
        self.completion = completion
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            completion(.success(appleIDCredential))
        } else {
            completion(.failure(AuthenticationError.invalidCredentials))
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        completion(.failure(error))
    }

    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}

// MARK: - Authentication Logger

private struct AuthenticationLogger {

    func log(_ message: String) {
        print("🔐 [AuthService] \(message)")
    }

    func logError(_ message: String, error: Error) {
        print("❌ [AuthService] \(message): \(error.localizedDescription)")
    }
}
