import Foundation
import FirebaseFirestore
import Combine

/// User profile service handling Firestore operations
/// 
/// This service manages user profile data with Firestore integration:
/// - CRUD operations for user profiles
/// - Real-time data synchronization
/// - Offline support with local caching
/// - Data validation and error handling
/// 
/// Uses @MainActor to ensure all UI updates happen on the main thread
@MainActor
class UserProfileService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published private(set) var userProfile: UserProfile?
    @Published private(set) var isLoading = false
    @Published private(set) var lastError: Error?
    
    // MARK: - Private Properties
    
    private let firestore = Firestore.firestore()
    private let logger = UserProfileLogger()
    private var profileListener: ListenerRegistration?
    
    // MARK: - Constants
    
    private enum Collections {
        static let users = "users"
        static let profiles = "profiles"
    }
    
    // MARK: - Initialization
    
    init() {
        setupFirestore()
        logger.log("UserProfileService initialized")
    }
    
    deinit {
        profileListener?.remove()
        logger.log("UserProfileService deinitialized")
    }
    
    // MARK: - Public Profile Methods
    
    /// Load user profile for the given user ID
    func loadUserProfile(for userId: String) async {
        logger.log("Loading profile for user: \(userId)")
        setLoading(true)
        clearError()
        
        do {
            let document = try await firestore
                .collection(Collections.profiles)
                .document(userId)
                .getDocument()
            
            if document.exists {
                let profile = try document.data(as: UserProfile.self)
                userProfile = profile
                logger.log("Profile loaded successfully for user: \(userId)")
                setupRealtimeListener(for: userId)
            } else {
                // Create default profile for new user
                let defaultProfile = UserProfile(id: userId)
                try await saveUserProfile(defaultProfile)
                logger.log("Created default profile for new user: \(userId)")
            }
            
        } catch {
            logger.logError("Failed to load profile", error: error)
            handleError(error)
        }
        
        setLoading(false)
    }
    
    /// Save user profile to Firestore
    func saveUserProfile(_ profile: UserProfile) async throws {
        logger.log("Saving profile for user: \(profile.id)")
        setLoading(true)
        clearError()
        
        do {
            var updatedProfile = profile
            updatedProfile.markAsUpdated()
            
            try await firestore
                .collection(Collections.profiles)
                .document(profile.id)
                .setData(from: updatedProfile)
            
            userProfile = updatedProfile
            logger.log("Profile saved successfully for user: \(profile.id)")
            
        } catch {
            logger.logError("Failed to save profile", error: error)
            handleError(error)
            throw error
        }
        
        setLoading(false)
    }
    
    /// Update specific profile fields
    func updateProfile(userId: String, updates: [String: Any]) async throws {
        logger.log("Updating profile fields for user: \(userId)")
        setLoading(true)
        clearError()
        
        do {
            var updateData = updates
            updateData["updatedAt"] = Timestamp(date: Date())
            updateData["version"] = FieldValue.increment(Int64(1))
            
            try await firestore
                .collection(Collections.profiles)
                .document(userId)
                .updateData(updateData)
            
            logger.log("Profile updated successfully for user: \(userId)")
            
        } catch {
            logger.logError("Failed to update profile", error: error)
            handleError(error)
            throw error
        }
        
        setLoading(false)
    }
    
    /// Delete user profile
    func deleteUserProfile(for userId: String) async throws {
        logger.log("Deleting profile for user: \(userId)")
        setLoading(true)
        clearError()
        
        do {
            try await firestore
                .collection(Collections.profiles)
                .document(userId)
                .delete()
            
            userProfile = nil
            profileListener?.remove()
            profileListener = nil
            
            logger.log("Profile deleted successfully for user: \(userId)")
            
        } catch {
            logger.logError("Failed to delete profile", error: error)
            handleError(error)
            throw error
        }
        
        setLoading(false)
    }
    
    // MARK: - Preference Update Methods
    
    /// Update food preferences
    func updateFoodPreferences(
        userId: String,
        strictExclusions: [String]? = nil,
        dietaryRestrictions: [DietaryRestriction]? = nil,
        allergies: [String]? = nil
    ) async throws {
        var updates: [String: Any] = [:]
        
        if let strictExclusions = strictExclusions {
            updates["strictExclusions"] = strictExclusions
        }
        if let dietaryRestrictions = dietaryRestrictions {
            updates["dietaryRestrictions"] = dietaryRestrictions.map { $0.rawValue }
        }
        if let allergies = allergies {
            updates["allergies"] = allergies
        }
        
        try await updateProfile(userId: userId, updates: updates)
    }
    
    /// Update family information
    func updateFamilyInfo(userId: String, familyInfo: FamilyInfo) async throws {
        let updates: [String: Any] = [
            "familyInfo": try Firestore.Encoder().encode(familyInfo)
        ]
        
        try await updateProfile(userId: userId, updates: updates)
    }
    
    /// Update notification settings
    func updateNotificationSettings(userId: String, settings: NotificationSettings) async throws {
        let updates: [String: Any] = [
            "notificationSettings": try Firestore.Encoder().encode(settings)
        ]
        
        try await updateProfile(userId: userId, updates: updates)
    }
    
    /// Update app preferences
    func updateAppPreferences(userId: String, preferences: AppPreferences) async throws {
        let updates: [String: Any] = [
            "appPreferences": try Firestore.Encoder().encode(preferences)
        ]
        
        try await updateProfile(userId: userId, updates: updates)
    }
    
    // MARK: - Computed Properties
    
    /// Returns true if a profile is currently loaded
    var hasProfile: Bool {
        userProfile != nil
    }
    
    /// Returns the current user's food restrictions
    var currentFoodRestrictions: [String] {
        guard let profile = userProfile else { return [] }
        return profile.allRestrictedIngredients
    }
    
    /// Returns true if user has any dietary restrictions
    var hasDietaryRestrictions: Bool {
        userProfile?.hasDietaryRestrictions ?? false
    }
}

// MARK: - Private Implementation

private extension UserProfileService {

    /// Setup Firestore configuration
    func setupFirestore() {
        let settings = FirestoreSettings()
        settings.isPersistenceEnabled = true
        settings.cacheSizeBytes = FirestoreCacheSizeUnlimited
        firestore.settings = settings

        logger.log("Firestore configured with offline persistence")
    }

    /// Setup real-time listener for profile changes
    func setupRealtimeListener(for userId: String) {
        // Remove existing listener
        profileListener?.remove()

        profileListener = firestore
            .collection(Collections.profiles)
            .document(userId)
            .addSnapshotListener { [weak self] documentSnapshot, error in
                Task { @MainActor in
                    self?.handleRealtimeUpdate(documentSnapshot, error: error)
                }
            }

        logger.log("Real-time listener setup for user: \(userId)")
    }

    /// Handle real-time profile updates
    func handleRealtimeUpdate(_ documentSnapshot: DocumentSnapshot?, error: Error?) {
        if let error = error {
            logger.logError("Real-time listener error", error: error)
            handleError(error)
            return
        }

        guard let document = documentSnapshot, document.exists else {
            logger.log("Profile document no longer exists")
            userProfile = nil
            return
        }

        do {
            let updatedProfile = try document.data(as: UserProfile.self)

            // Only update if the version is newer (avoid infinite loops)
            if let currentProfile = userProfile,
               updatedProfile.version <= currentProfile.version {
                logger.log("Ignoring older profile version: \(updatedProfile.version) <= \(currentProfile.version)")
                return
            }

            userProfile = updatedProfile
            logger.log("Profile updated via real-time listener (version: \(updatedProfile.version))")

        } catch {
            logger.logError("Failed to decode real-time profile update", error: error)
            handleError(error)
        }
    }

    /// Set loading state
    func setLoading(_ loading: Bool) {
        isLoading = loading
    }

    /// Handle and store errors
    func handleError(_ error: Error) {
        lastError = error
        logger.logError("UserProfileService error", error: error)
    }

    /// Clear the last error
    func clearError() {
        lastError = nil
    }
}

// MARK: - UserProfile Extensions for Firestore

extension UserProfile {

    /// Create a UserProfile from Firestore document data
    init(from document: DocumentSnapshot) throws {
        guard document.exists else {
            throw UserProfileError.documentNotFound
        }

        self = try document.data(as: UserProfile.self)
    }
}

// MARK: - UserProfile Service Errors

enum UserProfileError: LocalizedError, Sendable {
    case documentNotFound
    case invalidData
    case networkError
    case permissionDenied
    case quotaExceeded
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .documentNotFound:
            return "User profile not found"
        case .invalidData:
            return "Invalid profile data format"
        case .networkError:
            return "Network connection error"
        case .permissionDenied:
            return "Permission denied to access profile data"
        case .quotaExceeded:
            return "Storage quota exceeded"
        case .unknown(let message):
            return "Unknown error: \(message)"
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .documentNotFound:
            return "A new profile will be created automatically"
        case .invalidData:
            return "Please try updating your profile again"
        case .networkError:
            return "Check your internet connection and try again"
        case .permissionDenied:
            return "Please sign in again to access your profile"
        case .quotaExceeded:
            return "Please contact support for assistance"
        case .unknown:
            return "Please try again or contact support if the problem persists"
        }
    }
}

// MARK: - UserProfile Logger

private struct UserProfileLogger {

    func log(_ message: String) {
        print("👤 [ProfileService] \(message)")
    }

    func logError(_ message: String, error: Error) {
        print("❌ [ProfileService] \(message): \(error.localizedDescription)")
    }
}
