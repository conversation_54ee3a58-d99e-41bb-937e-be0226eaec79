import Foundation
import Combine

/// Data synchronization service coordinating authentication and profile services
/// 
/// This service acts as a coordinator between AuthenticationService and UserProfileService,
/// ensuring data consistency and proper synchronization flows.
/// 
/// Key responsibilities:
/// - Sync user profile when authentication state changes
/// - Handle offline/online state transitions
/// - Coordinate data cleanup on sign out
/// - Manage data migration and versioning
@MainActor
class UserDataSyncService: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published private(set) var syncState: SyncState = .idle
    @Published private(set) var lastSyncDate: Date?
    
    // MARK: - Dependencies
    
    private let authService: AuthenticationService
    private let profileService: UserProfileService
    private let logger = DataSyncLogger()
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(authService: AuthenticationService, profileService: UserProfileService) {
        self.authService = authService
        self.profileService = profileService
        
        setupSynchronization()
        logger.log("UserDataSyncService initialized")
    }
    
    // MARK: - Public Sync Methods
    
    /// Force a manual sync of user data
    func forceSyncUserData() async {
        guard let currentUser = authService.currentUser else {
            logger.log("No authenticated user for manual sync")
            return
        }
        
        logger.log("Starting manual sync for user: \(currentUser.id)")
        await syncUserProfile(for: currentUser)
    }
    
    /// Clear all local user data (used on sign out)
    func clearLocalUserData() async {
        logger.log("Clearing local user data")
        setSyncState(.clearing)
        
        // Clear profile service data
        // Note: ProfileService will handle its own cleanup
        
        setSyncState(.idle)
        lastSyncDate = nil
        logger.log("Local user data cleared")
    }
    
    /// Handle user account deletion
    func handleAccountDeletion(for userId: String) async {
        logger.log("Handling account deletion for user: \(userId)")
        setSyncState(.deleting)
        
        do {
            // Delete user profile from Firestore
            try await profileService.deleteUserProfile(for: userId)
            
            // Clear local data
            await clearLocalUserData()
            
            logger.log("Account deletion completed for user: \(userId)")
            
        } catch {
            logger.logError("Failed to complete account deletion", error: error)
            setSyncState(.error(DataSyncError.deletionFailed(error.localizedDescription)))
        }
    }
    
    // MARK: - Computed Properties
    
    /// Returns true if sync is currently in progress
    var isSyncing: Bool {
        switch syncState {
        case .syncing, .loading, .clearing, .deleting:
            return true
        default:
            return false
        }
    }
    
    /// Returns the current sync error, if any
    var currentError: DataSyncError? {
        if case .error(let error) = syncState {
            return error
        }
        return nil
    }
}

// MARK: - Private Implementation

private extension UserDataSyncService {
    
    /// Setup synchronization between auth and profile services
    func setupSynchronization() {
        // Listen to authentication state changes
        authService.$authenticationState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] authState in
                Task {
                    await self?.handleAuthenticationStateChange(authState)
                }
            }
            .store(in: &cancellables)
        
        logger.log("Synchronization setup completed")
    }
    
    /// Handle authentication state changes
    func handleAuthenticationStateChange(_ authState: AuthenticationState) async {
        switch authState {
        case .authenticated(let user):
            logger.log("User authenticated, syncing profile: \(user.id)")
            await syncUserProfile(for: user)
            
        case .unauthenticated:
            logger.log("User unauthenticated, clearing local data")
            await clearLocalUserData()
            
        case .loading:
            setSyncState(.loading)
            
        case .error(let error):
            logger.logError("Authentication error", error: error)
            setSyncState(.error(.authenticationFailed(error.localizedDescription)))
        }
    }
    
    /// Sync user profile data
    func syncUserProfile(for user: User) async {
        setSyncState(.syncing)
        
        do {
            // Load or create user profile
            await profileService.loadUserProfile(for: user.id)
            
            // Update profile with latest auth info if needed
            if let profile = profileService.userProfile {
                var updatedProfile = profile
                var needsUpdate = false
                
                // Update email if changed
                if updatedProfile.email != user.email {
                    updatedProfile.email = user.email
                    needsUpdate = true
                }
                
                // Update display name if changed
                if updatedProfile.displayName != user.displayName {
                    updatedProfile.displayName = user.displayName
                    needsUpdate = true
                }
                
                // Update photo URL if changed
                if updatedProfile.photoURL != user.photoURL {
                    updatedProfile.photoURL = user.photoURL
                    needsUpdate = true
                }
                
                // Save updates if needed
                if needsUpdate {
                    try await profileService.saveUserProfile(updatedProfile)
                    logger.log("Profile updated with latest auth info")
                }
            }
            
            setSyncState(.synced)
            lastSyncDate = Date()
            logger.log("User profile sync completed for: \(user.id)")
            
        } catch {
            logger.logError("Failed to sync user profile", error: error)
            setSyncState(.error(.profileSyncFailed(error.localizedDescription)))
        }
    }
    
    /// Set the current sync state
    func setSyncState(_ state: SyncState) {
        syncState = state
    }
}

// MARK: - Sync State

enum SyncState: Sendable, Equatable {
    case idle
    case loading
    case syncing
    case synced
    case clearing
    case deleting
    case error(DataSyncError)
    
    static func == (lhs: SyncState, rhs: SyncState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.syncing, .syncing),
             (.synced, .synced), (.clearing, .clearing), (.deleting, .deleting):
            return true
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError == rhsError
        default:
            return false
        }
    }
}

// MARK: - Data Sync Errors

enum DataSyncError: LocalizedError, Sendable, Equatable {
    case authenticationFailed(String)
    case profileSyncFailed(String)
    case deletionFailed(String)
    case networkError
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .authenticationFailed(let message):
            return "Authentication failed: \(message)"
        case .profileSyncFailed(let message):
            return "Profile sync failed: \(message)"
        case .deletionFailed(let message):
            return "Account deletion failed: \(message)"
        case .networkError:
            return "Network connection error during sync"
        case .unknown(let message):
            return "Unknown sync error: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .authenticationFailed:
            return "Please try signing in again"
        case .profileSyncFailed:
            return "Check your internet connection and try again"
        case .deletionFailed:
            return "Please try deleting your account again"
        case .networkError:
            return "Check your internet connection and try again"
        case .unknown:
            return "Please try again or contact support"
        }
    }
}

// MARK: - Data Sync Logger

private struct DataSyncLogger {
    
    func log(_ message: String) {
        print("🔄 [DataSync] \(message)")
    }
    
    func logError(_ message: String, error: Error) {
        print("❌ [DataSync] \(message): \(error.localizedDescription)")
    }
}
