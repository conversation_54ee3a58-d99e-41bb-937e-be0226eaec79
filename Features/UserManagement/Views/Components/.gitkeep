# Components Views Directory

This directory contains reusable UI components for the User Management module:

- UserAvatarView.swift - User avatar display component
- PreferenceToggleView.swift - Reusable preference toggle
- SettingRowView.swift - Standard settings row component

Components should:
- Be highly reusable
- Accept parameters for customization
- Follow consistent design patterns
- Include proper documentation
