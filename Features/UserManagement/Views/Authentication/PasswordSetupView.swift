import SwiftUI

/// Password setup view for new user registration
/// 
/// This view allows new users to create their password during registration.
/// Final step in the email-based registration flow.
/// 
/// Features:
/// - Password and confirmation input
/// - Password strength validation
/// - Password visibility toggle
/// - Account creation and automatic sign-in
struct PasswordSetupView: View {
    
    // MARK: - Properties
    
    let email: String
    let username: String
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var passwordError: String?
    @State private var confirmPasswordError: String?
    @State private var isPasswordVisible = false
    @State private var isConfirmPasswordVisible = false
    @State private var isLoading = false
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !password.isEmpty && 
        !confirmPassword.isEmpty && 
        passwordError == nil && 
        confirmPasswordError == nil &&
        password == confirmPassword &&
        password.count >= 6
    }
    
    private var passwordStrength: PasswordStrength {
        PasswordStrength.evaluate(password)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                headerSection
                passwordInputSection
                confirmPasswordInputSection
                passwordStrengthSection
                createAccountButtonSection
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Create account")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
            .alert("Account Creation Error", isPresented: .constant(authService.currentError != nil)) {
                Button("OK") {
                    // Error will be cleared automatically
                }
            } message: {
                if let error = authService.currentError {
                    Text(error.localizedDescription)
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Text("Using \(email)")
                .font(.title2)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Password Input Section
    
    private var passwordInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Password")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                Group {
                    if isPasswordVisible {
                        TextField("Enter password", text: $password)
                    } else {
                        SecureField("Enter password", text: $password)
                    }
                }
                .font(.body)
                .onChange(of: password) { _ in
                    validatePassword()
                    validateConfirmPassword()
                }
                
                Button(action: {
                    isPasswordVisible.toggle()
                }) {
                    Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                        .foregroundColor(.secondary)
                }
                .accessibilityLabel(isPasswordVisible ? "Hide password" : "Show password")
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
            
            if let passwordError = passwordError {
                Text(passwordError)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
    
    // MARK: - Confirm Password Input Section
    
    private var confirmPasswordInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Confirm Password")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                Group {
                    if isConfirmPasswordVisible {
                        TextField("Confirm password", text: $confirmPassword)
                    } else {
                        SecureField("Confirm password", text: $confirmPassword)
                    }
                }
                .font(.body)
                .onChange(of: confirmPassword) { _ in
                    validateConfirmPassword()
                }
                
                Button(action: {
                    isConfirmPasswordVisible.toggle()
                }) {
                    Image(systemName: isConfirmPasswordVisible ? "eye.slash" : "eye")
                        .foregroundColor(.secondary)
                }
                .accessibilityLabel(isConfirmPasswordVisible ? "Hide confirm password" : "Show confirm password")
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
            
            if let confirmPasswordError = confirmPasswordError {
                Text(confirmPasswordError)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
    
    // MARK: - Password Strength Section
    
    private var passwordStrengthSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            if !password.isEmpty {
                HStack {
                    Text("Password Strength:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(passwordStrength.description)
                        .font(.caption)
                        .foregroundColor(passwordStrength.color)
                }
                
                ProgressView(value: passwordStrength.score, total: 1.0)
                    .progressViewStyle(LinearProgressViewStyle(tint: passwordStrength.color))
            }
        }
    }
    
    // MARK: - Create Account Button Section
    
    private var createAccountButtonSection: some View {
        Button(action: {
            Task {
                await createAccount()
            }
        }) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                Text("Create Account")
                    .font(.headline)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(isFormValid && !isLoading ? Color.pink : Color.gray)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .disabled(!isFormValid || isLoading)
        .accessibilityLabel("Create account")
    }
    
    // MARK: - Private Methods
    
    private func validatePassword() {
        passwordError = nil
        
        guard !password.isEmpty else { return }
        
        if password.count < 6 {
            passwordError = "Password must be at least 6 characters"
            return
        }
        
        if password.count > 128 {
            passwordError = "Password must be less than 128 characters"
            return
        }
    }
    
    private func validateConfirmPassword() {
        confirmPasswordError = nil
        
        guard !confirmPassword.isEmpty else { return }
        
        if password != confirmPassword {
            confirmPasswordError = "Passwords do not match"
        }
    }
    
    private func createAccount() async {
        guard isFormValid else { return }
        
        isLoading = true
        
        await authService.createAccount(
            email: email,
            password: password,
            displayName: username
        )
        
        // Check if account creation was successful
        if authService.currentUser != nil {
            // Success - dismiss all registration views
            await MainActor.run {
                dismiss()
            }
        } else {
            await MainActor.run {
                isLoading = false
            }
        }
    }
}

// MARK: - Password Strength Enum

enum PasswordStrength {
    case weak
    case fair
    case good
    case strong
    
    var description: String {
        switch self {
        case .weak: return "Weak"
        case .fair: return "Fair"
        case .good: return "Good"
        case .strong: return "Strong"
        }
    }
    
    var color: Color {
        switch self {
        case .weak: return .red
        case .fair: return .orange
        case .good: return .yellow
        case .strong: return .green
        }
    }
    
    var score: Double {
        switch self {
        case .weak: return 0.25
        case .fair: return 0.5
        case .good: return 0.75
        case .strong: return 1.0
        }
    }
    
    static func evaluate(_ password: String) -> PasswordStrength {
        var score = 0
        
        if password.count >= 8 { score += 1 }
        if password.rangeOfCharacter(from: .lowercaseLetters) != nil { score += 1 }
        if password.rangeOfCharacter(from: .uppercaseLetters) != nil { score += 1 }
        if password.rangeOfCharacter(from: .decimalDigits) != nil { score += 1 }
        if password.rangeOfCharacter(from: CharacterSet(charactersIn: "!@#$%^&*()_+-=[]{}|;:,.<>?")) != nil { score += 1 }
        
        switch score {
        case 0...1: return .weak
        case 2: return .fair
        case 3...4: return .good
        case 5: return .strong
        default: return .weak
        }
    }
}

// MARK: - Preview

struct PasswordSetupView_Previews: PreviewProvider {
    static var previews: some View {
        PasswordSetupView(email: "<EMAIL>", username: "testuser")
            .environmentObject(AuthenticationService())
    }
}
