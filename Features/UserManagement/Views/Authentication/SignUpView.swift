import SwiftUI

/// User registration interface with validation and accessibility
/// 
/// Provides a clean, accessible interface for new user registration
/// with comprehensive form validation and error handling.
/// 
/// Features:
/// - Email and password registration
/// - Real-time form validation
/// - Password strength indicator
/// - Accessibility support
/// - Consistent design with LoginView
struct SignUpView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var appPreferences: AppPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var displayName = ""
    @State private var showingPassword = false
    @State private var showingConfirmPassword = false
    @State private var agreedToTerms = false
    
    // Validation states
    @State private var emailError: String?
    @State private var passwordError: String?
    @State private var confirmPasswordError: String?
    @State private var displayNameError: String?
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !email.isEmpty && !password.isEmpty && !confirmPassword.isEmpty &&
        emailError == nil && passwordError == nil && confirmPasswordError == nil &&
        agreedToTerms
    }
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }
    
    private var passwordStrength: PasswordStrength {
        PasswordStrength.evaluate(password)
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24 * fontScale) {
                    headerSection
                    formSection
                    termsSection
                    signUpButtonSection
                    signInPromptSection
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 32)
            }
            .navigationTitle("Create Account")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(.body)
                    .scaleEffect(fontScale)
                }
            }
            .background(Color(.systemGroupedBackground))
            .overlay(loadingOverlay)
            .alert("Sign Up Error", isPresented: .constant(authService.currentError != nil)) {
                Button("OK") {
                    // Error will be cleared automatically
                }
            } message: {
                if let error = authService.currentError {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(error.localizedDescription)
                        if let suggestion = error.recoverySuggestion {
                            Text(suggestion)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16 * fontScale) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 60 * fontScale))
                .foregroundColor(.accentColor)
                .accessibilityHidden(true)
            
            VStack(spacing: 8 * fontScale) {
                Text("Join Ingredient Scanner")
                    .font(.title2.weight(.semibold))
                    .multilineTextAlignment(.center)
                    .scaleEffect(fontScale)
                
                Text("Create your account to save preferences and sync across devices")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .scaleEffect(fontScale)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Create account to join Ingredient Scanner")
    }
    
    // MARK: - Form Section
    
    private var formSection: some View {
        VStack(spacing: 20 * fontScale) {
            // Display Name Field (Optional)
            VStack(alignment: .leading, spacing: 8) {
                Text("Display Name (Optional)")
                    .font(.headline)
                    .scaleEffect(fontScale)
                
                TextField("Enter your name", text: $displayName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.words)
                    .font(.body)
                    .scaleEffect(fontScale)
                    .onChange(of: displayName) { _ in
                        validateDisplayName()
                    }
                    .accessibilityLabel("Display name, optional")
                
                if let displayNameError = displayNameError {
                    Text(displayNameError)
                        .font(.caption)
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)
                }
            }
            
            // Email Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Email Address")
                    .font(.headline)
                    .scaleEffect(fontScale)
                
                TextField("Enter your email", text: $email)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .font(.body)
                    .scaleEffect(fontScale)
                    .onChange(of: email) { _ in
                        validateEmail()
                    }
                    .accessibilityLabel("Email address")
                
                if let emailError = emailError {
                    Text(emailError)
                        .font(.caption)
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)
                }
            }
            
            // Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Password")
                    .font(.headline)
                    .scaleEffect(fontScale)
                
                HStack {
                    Group {
                        if showingPassword {
                            TextField("Create a password", text: $password)
                        } else {
                            SecureField("Create a password", text: $password)
                        }
                    }
                    .font(.body)
                    .scaleEffect(fontScale)
                    .onChange(of: password) { _ in
                        validatePassword()
                        validateConfirmPassword()
                    }
                    
                    Button(action: {
                        showingPassword.toggle()
                    }) {
                        Image(systemName: showingPassword ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                    .accessibilityLabel(showingPassword ? "Hide password" : "Show password")
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
                
                // Password Strength Indicator
                if !password.isEmpty {
                    PasswordStrengthView(strength: passwordStrength, fontScale: fontScale)
                }
                
                if let passwordError = passwordError {
                    Text(passwordError)
                        .font(.caption)
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)
                }
            }
            
            // Confirm Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Confirm Password")
                    .font(.headline)
                    .scaleEffect(fontScale)
                
                HStack {
                    Group {
                        if showingConfirmPassword {
                            TextField("Confirm your password", text: $confirmPassword)
                        } else {
                            SecureField("Confirm your password", text: $confirmPassword)
                        }
                    }
                    .font(.body)
                    .scaleEffect(fontScale)
                    .onChange(of: confirmPassword) { _ in
                        validateConfirmPassword()
                    }
                    
                    Button(action: {
                        showingConfirmPassword.toggle()
                    }) {
                        Image(systemName: showingConfirmPassword ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                    .accessibilityLabel(showingConfirmPassword ? "Hide password confirmation" : "Show password confirmation")
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
                
                if let confirmPasswordError = confirmPasswordError {
                    Text(confirmPasswordError)
                        .font(.caption)
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)
                }
            }
        }
    }
    
    // MARK: - Terms Section
    
    private var termsSection: some View {
        HStack(alignment: .top, spacing: 12) {
            Button(action: {
                agreedToTerms.toggle()
            }) {
                Image(systemName: agreedToTerms ? "checkmark.square.fill" : "square")
                    .font(.title3)
                    .foregroundColor(agreedToTerms ? .accentColor : .secondary)
            }
            .accessibilityLabel(agreedToTerms ? "Terms agreed" : "Agree to terms")
            
            VStack(alignment: .leading, spacing: 4) {
                Text("I agree to the Terms of Service and Privacy Policy")
                    .font(.subheadline)
                    .scaleEffect(fontScale)
                
                Text("By creating an account, you agree to our terms and privacy practices.")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)
            }
            
            Spacer()
        }
        .accessibilityElement(children: .combine)
    }
    
    // MARK: - Sign Up Button Section
    
    private var signUpButtonSection: some View {
        Button(action: {
            Task {
                await authService.createAccount(
                    email: email,
                    password: password,
                    displayName: displayName.isEmpty ? nil : displayName
                )
            }
        }) {
            HStack {
                if authService.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                Text("Create Account")
                    .font(.headline)
                    .scaleEffect(fontScale)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50 * fontScale)
            .background(isFormValid ? Color.accentColor : Color.gray)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .disabled(!isFormValid || authService.isLoading)
        .accessibilityLabel("Create new account")
    }
    
    // MARK: - Sign In Prompt Section
    
    private var signInPromptSection: some View {
        VStack(spacing: 16 * fontScale) {
            Text("Already have an account?")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .scaleEffect(fontScale)
            
            Button("Sign In") {
                dismiss()
            }
            .font(.headline)
            .foregroundColor(.accentColor)
            .scaleEffect(fontScale)
            .accessibilityLabel("Sign in to existing account")
        }
    }
    
    // MARK: - Loading Overlay
    
    @ViewBuilder
    private var loadingOverlay: some View {
        if authService.isLoading {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .overlay(
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.5)
                        
                        Text("Creating Account...")
                            .font(.headline)
                            .foregroundColor(.white)
                            .scaleEffect(fontScale)
                    }
                    .padding(32)
                    .background(Color.black.opacity(0.8))
                    .cornerRadius(16)
                )
                .accessibilityLabel("Creating account, please wait")
        }
    }
}

// MARK: - Private Methods

private extension SignUpView {

    func validateDisplayName() {
        displayNameError = nil

        guard !displayName.isEmpty else { return }

        if displayName.count > 50 {
            displayNameError = "Display name must be 50 characters or less"
        }
    }

    func validateEmail() {
        emailError = nil

        guard !email.isEmpty else { return }

        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)

        if !emailPredicate.evaluate(with: email) {
            emailError = "Please enter a valid email address"
        }
    }

    func validatePassword() {
        passwordError = nil

        guard !password.isEmpty else { return }

        if password.count < 8 {
            passwordError = "Password must be at least 8 characters"
        } else if passwordStrength == .weak {
            passwordError = "Password is too weak. Add numbers, symbols, or mix case."
        }
    }

    func validateConfirmPassword() {
        confirmPasswordError = nil

        guard !confirmPassword.isEmpty else { return }

        if password != confirmPassword {
            confirmPasswordError = "Passwords do not match"
        }
    }
}

// MARK: - Password Strength

enum PasswordStrength: CaseIterable {
    case weak, fair, good, strong

    var color: Color {
        switch self {
        case .weak: return .red
        case .fair: return .orange
        case .good: return .yellow
        case .strong: return .green
        }
    }

    var text: String {
        switch self {
        case .weak: return "Weak"
        case .fair: return "Fair"
        case .good: return "Good"
        case .strong: return "Strong"
        }
    }

    var progress: Double {
        switch self {
        case .weak: return 0.25
        case .fair: return 0.5
        case .good: return 0.75
        case .strong: return 1.0
        }
    }

    static func evaluate(_ password: String) -> PasswordStrength {
        var score = 0

        // Length check
        if password.count >= 8 { score += 1 }
        if password.count >= 12 { score += 1 }

        // Character variety checks
        if password.rangeOfCharacter(from: .lowercaseLetters) != nil { score += 1 }
        if password.rangeOfCharacter(from: .uppercaseLetters) != nil { score += 1 }
        if password.rangeOfCharacter(from: .decimalDigits) != nil { score += 1 }
        if password.rangeOfCharacter(from: CharacterSet(charactersIn: "!@#$%^&*()_+-=[]{}|;:,.<>?")) != nil { score += 1 }

        switch score {
        case 0...2: return .weak
        case 3...4: return .fair
        case 5: return .good
        default: return .strong
        }
    }
}

// MARK: - Password Strength View

struct PasswordStrengthView: View {
    let strength: PasswordStrength
    let fontScale: CGFloat

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Password Strength:")
                    .font(.caption)
                    .scaleEffect(fontScale)

                Text(strength.text)
                    .font(.caption.weight(.semibold))
                    .foregroundColor(strength.color)
                    .scaleEffect(fontScale)

                Spacer()
            }

            ProgressView(value: strength.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: strength.color))
                .scaleEffect(y: 0.5)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Password strength: \(strength.text)")
    }
}

// MARK: - Preview

struct SignUpView_Previews: PreviewProvider {
    static var previews: some View {
        SignUpView()
            .environmentObject(AuthenticationService())
            .environmentObject(AppPreferences())
    }
}
