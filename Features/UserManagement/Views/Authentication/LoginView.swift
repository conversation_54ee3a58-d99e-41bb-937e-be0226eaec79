import SwiftUI
import AuthenticationServices

/// Main login interface supporting multiple authentication methods
/// 
/// Designed with accessibility and user experience in mind, particularly
/// for users like <PERSON> (tech novice) who need clear, simple interfaces.
/// 
/// Features:
/// - Apple Sign-In with native button
/// - Google Sign-In with custom styling
/// - Email/Password authentication
/// - Clear error messaging and loading states
/// - Accessibility support with large fonts
struct LoginView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var appPreferences: AppPreferences
    
    // MARK: - State Properties
    
    @State private var showingSignUp = false
    @State private var email = ""
    @State private var password = ""
    @State private var showingPassword = false
    @State private var emailError: String?
    @State private var passwordError: String?
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !email.isEmpty && !password.isEmpty && emailError == nil && passwordError == nil
    }
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }

    /// Optimized computed property to reduce view updates
    private var isEmailValid: Bool {
        emailError == nil && !email.isEmpty
    }

    /// Optimized computed property to reduce view updates
    private var isPasswordValid: Bool {
        passwordError == nil && !password.isEmpty
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32 * fontScale) {
                    headerSection
                    socialSignInSection
                    dividerSection
                    emailSignInSection
                    signUpPromptSection
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 32)
            }
            .navigationTitle("Welcome Back")
            .navigationBarTitleDisplayMode(.large)
            .background(Color(.systemGroupedBackground))
            .overlay(loadingOverlay)
            .alert("Sign In Error", isPresented: .constant(authService.currentError != nil)) {
                Button("OK") {
                    // Error will be cleared automatically
                }
            } message: {
                if let error = authService.currentError {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(error.localizedDescription)
                        if let suggestion = error.recoverySuggestion {
                            Text(suggestion)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingSignUp) {
            SignUpView()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16 * fontScale) {
            Image(systemName: "person.circle.fill")
                .font(.system(size: 80 * fontScale))
                .foregroundColor(.accentColor)
                .accessibilityHidden(true)
            
            VStack(spacing: 8 * fontScale) {
                Text("Sign In to Your Account")
                    .font(.title2.weight(.semibold))
                    .multilineTextAlignment(.center)
                    .scaleEffect(fontScale)
                
                Text("Access your personalized ingredient preferences and recipe recommendations")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .scaleEffect(fontScale)
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Sign in to access your personalized account")
    }
    
    // MARK: - Social Sign-In Section
    
    private var socialSignInSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Apple Sign-In Button
            SignInWithAppleButton(
                onRequest: { request in
                    request.requestedScopes = [.fullName, .email]
                },
                onCompletion: { result in
                    // Handle Apple Sign-In result
                    Task {
                        await authService.signInWithApple()
                    }
                }
            )
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50 * fontScale)
            .cornerRadius(8)
            .disabled(authService.isLoading)
            
            // Google Sign-In Button
            Button(action: {
                Task {
                    await authService.signInWithGoogle()
                }
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "globe")
                        .font(.title3)
                    Text("Continue with Google")
                        .font(.headline)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50 * fontScale)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(authService.isLoading)
            .accessibilityLabel("Sign in with Google")
        }
    }
    
    // MARK: - Divider Section
    
    private var dividerSection: some View {
        HStack {
            Rectangle()
                .frame(height: 1)
                .foregroundColor(.secondary.opacity(0.3))
            
            Text("or")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.horizontal, 16)
                .scaleEffect(fontScale)
            
            Rectangle()
                .frame(height: 1)
                .foregroundColor(.secondary.opacity(0.3))
        }
    }
    
    // MARK: - Email Sign-In Section
    
    private var emailSignInSection: some View {
        VStack(spacing: 20 * fontScale) {
            // Email Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Email")
                    .font(.headline)
                    .scaleEffect(fontScale)
                
                TextField("Enter your email", text: $email)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .font(.body)
                    .scaleEffect(fontScale)
                    .onChange(of: email) { _ in
                        validateEmail()
                    }
                    .accessibilityLabel("Email address")
                
                if let emailError = emailError {
                    Text(emailError)
                        .font(.caption)
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)
                }
            }
            
            // Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Password")
                    .font(.headline)
                    .scaleEffect(fontScale)
                
                HStack {
                    Group {
                        if showingPassword {
                            TextField("Enter your password", text: $password)
                        } else {
                            SecureField("Enter your password", text: $password)
                        }
                    }
                    .font(.body)
                    .scaleEffect(fontScale)
                    .onChange(of: password) { _ in
                        validatePassword()
                    }
                    
                    Button(action: {
                        showingPassword.toggle()
                    }) {
                        Image(systemName: showingPassword ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                    .accessibilityLabel(showingPassword ? "Hide password" : "Show password")
                }
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .accessibilityElement(children: .contain)
                
                if let passwordError = passwordError {
                    Text(passwordError)
                        .font(.caption)
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)
                }
            }
            
            // Sign In Button
            Button(action: {
                Task {
                    await authService.signInWithEmail(email, password: password)
                }
            }) {
                HStack {
                    if authService.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    Text("Sign In")
                        .font(.headline)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50 * fontScale)
                .background(isFormValid ? Color.accentColor : Color.gray)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(!isFormValid || authService.isLoading)
            .accessibilityLabel("Sign in with email and password")

            // Developer Button (for testing/development)
            Button(action: {
                Task {
                    await authService.signInAsDeveloper()
                }
            }) {
                HStack {
                    Image(systemName: "hammer.fill")
                        .font(.title3)
                    Text("Developer")
                        .font(.headline)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50 * fontScale)
                .background(Color.orange)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .disabled(authService.isLoading)
            .accessibilityLabel("Developer sign in - skip authentication")
        }
    }

    // MARK: - Sign Up Prompt Section
    
    private var signUpPromptSection: some View {
        VStack(spacing: 16 * fontScale) {
            Text("Don't have an account?")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .scaleEffect(fontScale)
            
            Button("Create Account") {
                showingSignUp = true
            }
            .font(.headline)
            .foregroundColor(.accentColor)
            .scaleEffect(fontScale)
            .accessibilityLabel("Create a new account")
        }
    }
    
    // MARK: - Loading Overlay
    
    @ViewBuilder
    private var loadingOverlay: some View {
        if authService.isLoading {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .overlay(
                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.5)
                        
                        Text("Signing In...")
                            .font(.headline)
                            .foregroundColor(.white)
                            .scaleEffect(fontScale)
                    }
                    .padding(32)
                    .background(Color.black.opacity(0.8))
                    .cornerRadius(16)
                )
                .accessibilityLabel("Signing in, please wait")
        }
    }
}

// MARK: - Private Methods

private extension LoginView {
    
    func validateEmail() {
        emailError = nil
        
        guard !email.isEmpty else { return }
        
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        
        if !emailPredicate.evaluate(with: email) {
            emailError = "Please enter a valid email address"
        }
    }
    
    func validatePassword() {
        passwordError = nil
        
        guard !password.isEmpty else { return }
        
        if password.count < 6 {
            passwordError = "Password must be at least 6 characters"
        }
    }
}

// MARK: - Preview

struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView()
            .environmentObject(AuthenticationService())
            .environmentObject(AppPreferences())
    }
}
