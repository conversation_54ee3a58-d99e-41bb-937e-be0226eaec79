import SwiftUI
import FirebaseAuth

/// Password input view for existing users
/// 
/// This view allows existing users to enter their password to sign in.
/// Displays the email address and provides password input with show/hide functionality.
/// 
/// Features:
/// - Password visibility toggle
/// - Error handling for incorrect passwords
/// - Forgot password functionality
/// - Clean, accessible interface
struct PasswordInputView: View {
    
    // MARK: - Properties
    
    let email: String
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var password = ""
    @State private var passwordError: String?
    @State private var isPasswordVisible = false
    @State private var isLoading = false
    @State private var showingForgotPassword = false
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !password.isEmpty && passwordError == nil
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                headerSection
                passwordInputSection
                signInButtonSection
                forgotPasswordSection
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Sign In")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
            .alert("Sign In Error", isPresented: .constant(authService.currentError != nil)) {
                Button("OK") {
                    // Error will be cleared automatically
                }
            } message: {
                if let error = authService.currentError {
                    Text(error.localizedDescription)
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Text("Using \(email)")
                .font(.title2)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Password Input Section
    
    private var passwordInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Password")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                Group {
                    if isPasswordVisible {
                        TextField("Enter password", text: $password)
                    } else {
                        SecureField("Enter password", text: $password)
                    }
                }
                .font(.body)
                .onChange(of: password) { _ in
                    validatePassword()
                }
                
                Button(action: {
                    isPasswordVisible.toggle()
                }) {
                    Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                        .foregroundColor(.secondary)
                }
                .accessibilityLabel(isPasswordVisible ? "Hide password" : "Show password")
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
            
            if let passwordError = passwordError {
                Text(passwordError)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
    
    // MARK: - Sign In Button Section
    
    private var signInButtonSection: some View {
        Button(action: {
            Task {
                await signIn()
            }
        }) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                Text("Sign in")
                    .font(.headline)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(isFormValid && !isLoading ? Color.pink : Color.gray)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .disabled(!isFormValid || isLoading)
        .accessibilityLabel("Sign in with password")
    }
    
    // MARK: - Forgot Password Section
    
    private var forgotPasswordSection: some View {
        Button("Forgot password?") {
            showingForgotPassword = true
        }
        .font(.body)
        .foregroundColor(.blue)
        .accessibilityLabel("Forgot password")
        .alert("Reset Password", isPresented: $showingForgotPassword) {
            Button("Send Reset Email") {
                Task {
                    await sendPasswordReset()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("We'll send a password reset link to \(email)")
        }
    }
    
    // MARK: - Private Methods
    
    private func validatePassword() {
        passwordError = nil
        
        guard !password.isEmpty else { return }
        
        if password.count < 6 {
            passwordError = "Password must be at least 6 characters"
        }
    }
    
    private func signIn() async {
        guard isFormValid else { return }
        
        isLoading = true
        passwordError = nil
        
        await authService.signInWithEmail(email, password: password)
        
        // Check if sign in was successful
        if authService.currentUser != nil {
            // Success - dismiss the view
            await MainActor.run {
                dismiss()
            }
        } else {
            // Handle error
            await MainActor.run {
                isLoading = false
                passwordError = "Incorrect password. Please try again."
            }
        }
    }
    
    private func sendPasswordReset() async {
        do {
            try await Auth.auth().sendPasswordReset(withEmail: email)
            // Show success message
        } catch {
            // Handle error
        }
    }
}

// MARK: - Preview

struct PasswordInputView_Previews: PreviewProvider {
    static var previews: some View {
        PasswordInputView(email: "<EMAIL>")
            .environmentObject(AuthenticationService())
    }
}
