import SwiftUI

/// Email input view for the first step of email authentication
/// 
/// This view allows users to enter their email address and checks if they
/// have an existing account or need to create a new one.
/// 
/// Features:
/// - Email validation
/// - Clean, accessible interface
/// - Navigation to appropriate next step
/// - Error handling and loading states
struct EmailInputView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var email = ""
    @State private var emailError: String?
    @State private var isLoading = false
    @State private var showingPasswordInput = false
    @State private var showingRegistration = false
    @State private var isExistingUser = false
    
    // MARK: - Computed Properties
    
    private var isEmailValid: Bool {
        !email.isEmpty && emailError == nil
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                headerSection
                emailInputSection
                continueButtonSection
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Continue with <PERSON><PERSON>")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
            .sheet(isPresented: $showingPasswordInput) {
                PasswordInputView(email: email)
            }
            .sheet(isPresented: $showingRegistration) {
                UsernameInputView(email: email)
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Text("We'll check if you have an account, and help create one if you don't")
                .font(.title3)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Email Input Section
    
    private var emailInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Email")
                .font(.headline)
                .foregroundColor(.primary)
            
            TextField("<EMAIL>", text: $email)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
                .autocorrectionDisabled()
                .font(.body)
                .onChange(of: email) { _ in
                    validateEmail()
                }
                .accessibilityLabel("Email address")
            
            if let emailError = emailError {
                Text(emailError)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
    
    // MARK: - Continue Button Section
    
    private var continueButtonSection: some View {
        Button(action: {
            Task {
                await checkEmailAndProceed()
            }
        }) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                Text("Continue")
                    .font(.headline)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(isEmailValid && !isLoading ? Color.pink : Color.gray)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .disabled(!isEmailValid || isLoading)
        .accessibilityLabel("Continue with email")
    }
    
    // MARK: - Private Methods
    
    private func validateEmail() {
        emailError = nil
        
        guard !email.isEmpty else { return }
        
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        
        if !emailPredicate.evaluate(with: email) {
            emailError = "Please enter a valid email address"
        }
    }
    
    private func checkEmailAndProceed() async {
        guard isEmailValid else { return }
        
        isLoading = true
        
        do {
            let exists = try await authService.checkEmailExists(email)
            
            await MainActor.run {
                isLoading = false
                
                if exists {
                    // Existing user - show password input
                    isExistingUser = true
                    showingPasswordInput = true
                } else {
                    // New user - show registration flow
                    isExistingUser = false
                    showingRegistration = true
                }
            }
            
        } catch {
            await MainActor.run {
                isLoading = false
                emailError = "Unable to verify email. Please try again."
            }
        }
    }
}

// MARK: - Preview

struct EmailInputView_Previews: PreviewProvider {
    static var previews: some View {
        EmailInputView()
            .environmentObject(AuthenticationService())
    }
}
