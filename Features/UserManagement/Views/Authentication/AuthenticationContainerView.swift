import SwiftUI

/// Container view managing the authentication flow
/// 
/// This view serves as the main entry point for authentication,
/// handling the transition between login and signup flows.
/// 
/// Features:
/// - Seamless login/signup transition
/// - Consistent state management
/// - Accessibility support
/// - Integration ready for Profile tab
struct AuthenticationContainerView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var syncService: UserDataSyncService
    @EnvironmentObject private var appPreferences: AppPreferences
    
    // MARK: - State Properties
    
    @StateObject private var loginViewModel: LoginViewModel
    @State private var showingSignUp = false
    
    // MARK: - Initialization
    
    init() {
        // Initialize with a placeholder - will be properly set in onAppear
        self._loginViewModel = StateObject(wrappedValue: LoginViewModel(authService: AuthenticationService()))
    }
    
    // MARK: - Body
    
    var body: some View {
        Group {
            if authService.isAuthenticated {
                // User is authenticated - show success state
                AuthenticatedView()
            } else {
                // User is not authenticated - show login/signup
                authenticationFlow
            }
        }
        .onAppear {
            // Properly initialize the view model with the environment auth service
            if loginViewModel.authService !== authService {
                loginViewModel = LoginViewModel(authService: authService)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: authService.isAuthenticated)
    }
    
    // MARK: - Authentication Flow
    
    @ViewBuilder
    private var authenticationFlow: some View {
        if showingSignUp {
            SignUpView()
                .environmentObject(loginViewModel)
                .transition(.asymmetric(
                    insertion: .move(edge: .trailing),
                    removal: .move(edge: .leading)
                ))
        } else {
            LoginView()
                .environmentObject(loginViewModel)
                .transition(.asymmetric(
                    insertion: .move(edge: .leading),
                    removal: .move(edge: .trailing)
                ))
        }
    }
}

// MARK: - Authenticated View

struct AuthenticatedView: View {
    
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var syncService: UserDataSyncService
    
    var body: some View {
        VStack(spacing: 24) {
            // Success Header
            VStack(spacing: 16) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
                    .accessibilityHidden(true)
                
                Text("Welcome!")
                    .font(.title.weight(.semibold))
                
                if let user = authService.currentUser {
                    Text("Signed in as \(user.bestDisplayName)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            
            // Sync Status
            syncStatusView
            
            // Quick Actions
            VStack(spacing: 16) {
                Button("Continue to Profile") {
                    // This will be handled by the parent Profile view
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                
                Button("Sign Out") {
                    Task {
                        await authService.signOut()
                    }
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
            }
        }
        .padding(32)
        .background(Color(.systemGroupedBackground))
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Successfully signed in")
    }
    
    @ViewBuilder
    private var syncStatusView: some View {
        HStack(spacing: 12) {
            Group {
                switch syncService.syncState {
                case .syncing, .loading:
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Syncing your data...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                case .synced:
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Data synced")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                case .error(let error):
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text("Sync error: \(error.localizedDescription)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                default:
                    Image(systemName: "cloud.fill")
                        .foregroundColor(.blue)
                    Text("Ready to sync")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(8)
        .accessibilityElement(children: .combine)
    }
}

// MARK: - Enhanced LoginView Integration

extension LoginView {
    
    /// Initialize with view model for container integration
    init(viewModel: LoginViewModel) {
        // This would be used if we need to pass the view model directly
        // For now, we use environment objects
    }
}

extension SignUpView {
    
    /// Initialize with view model for container integration
    init(viewModel: LoginViewModel) {
        // This would be used if we need to pass the view model directly
        // For now, we use environment objects
    }
}

// MARK: - LoginViewModel Environment Extension

extension LoginViewModel: ObservableObject {
    
    /// Convenience property for authentication service access
    var authService: AuthenticationService {
        return authService
    }
}

// MARK: - Preview

struct AuthenticationContainerView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Unauthenticated state
            AuthenticationContainerView()
                .environmentObject(AuthenticationService())
                .environmentObject(UserDataSyncService(
                    authService: AuthenticationService(),
                    profileService: UserProfileService()
                ))
                .environmentObject(AppPreferences())
                .previewDisplayName("Login Flow")
            
            // Authenticated state
            AuthenticatedView()
                .environmentObject({
                    let service = AuthenticationService()
                    // Mock authenticated state for preview
                    return service
                }())
                .environmentObject(UserDataSyncService(
                    authService: AuthenticationService(),
                    profileService: UserProfileService()
                ))
                .previewDisplayName("Authenticated")
        }
    }
}

// MARK: - Accessibility Helpers

extension AuthenticationContainerView {
    
    /// Accessibility announcement for state changes
    private func announceStateChange() {
        if authService.isAuthenticated {
            UIAccessibility.post(notification: .announcement, argument: "Successfully signed in")
        } else {
            UIAccessibility.post(notification: .announcement, argument: "Signed out")
        }
    }
}

// MARK: - Animation Helpers

extension AnyTransition {
    
    /// Custom slide transition for authentication flow
    static var authenticationSlide: AnyTransition {
        .asymmetric(
            insertion: .move(edge: .trailing).combined(with: .opacity),
            removal: .move(edge: .leading).combined(with: .opacity)
        )
    }
}
