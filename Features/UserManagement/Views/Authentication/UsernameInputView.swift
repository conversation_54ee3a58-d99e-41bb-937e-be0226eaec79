import SwiftUI

/// Username input view for new user registration
/// 
/// This view allows new users to set their username during the registration process.
/// Part of the multi-step registration flow for email-based authentication.
/// 
/// Features:
/// - Username validation and availability checking
/// - Clean, accessible interface
/// - Navigation to password setup
/// - Real-time feedback
struct UsernameInputView: View {
    
    // MARK: - Properties
    
    let email: String
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var username = ""
    @State private var usernameError: String?
    @State private var isLoading = false
    @State private var showingPasswordSetup = false
    
    // MARK: - Computed Properties
    
    private var isUsernameValid: Bool {
        !username.isEmpty && usernameError == nil && username.count >= 3
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                headerSection
                usernameInputSection
                continueButtonSection
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 32)
            .navigationTitle("Create account")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "arrow.left")
                            .font(.title2)
                            .foregroundColor(.primary)
                    }
                }
            }
            .sheet(isPresented: $showingPasswordSetup) {
                PasswordSetupView(email: email, username: username)
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Text("Using \(email)")
                .font(.title2)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Username Input Section
    
    private var usernameInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Name")
                .font(.headline)
                .foregroundColor(.primary)
            
            HStack {
                Text("@")
                    .font(.title2)
                    .foregroundColor(.primary)
                    .padding(.leading, 8)
                
                TextField("aaaa", text: $username)
                    .font(.body)
                    .autocapitalization(.none)
                    .autocorrectionDisabled()
                    .onChange(of: username) { _ in
                        validateUsername()
                    }
                    .accessibilityLabel("Username")
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
            
            if let usernameError = usernameError {
                Text(usernameError)
                    .font(.caption)
                    .foregroundColor(.red)
            } else if !username.isEmpty && isUsernameValid {
                Text("Your @username is unique. You can always change it later.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Continue Button Section
    
    private var continueButtonSection: some View {
        Button(action: {
            proceedToPasswordSetup()
        }) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                Text("Continue")
                    .font(.headline)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(isUsernameValid && !isLoading ? Color.pink : Color.gray)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .disabled(!isUsernameValid || isLoading)
        .accessibilityLabel("Continue to password setup")
    }
    
    // MARK: - Private Methods
    
    private func validateUsername() {
        usernameError = nil
        
        guard !username.isEmpty else { return }
        
        // Remove any spaces and special characters except letters, numbers, and underscores
        let filteredUsername = username.filter { $0.isLetter || $0.isNumber || $0 == "_" }
        if filteredUsername != username {
            username = filteredUsername
        }
        
        if username.count < 3 {
            usernameError = "Username must be at least 3 characters"
            return
        }
        
        if username.count > 20 {
            usernameError = "Username must be less than 20 characters"
            return
        }
        
        // Check for valid characters
        let usernameRegex = "^[a-zA-Z0-9_]+$"
        let usernamePredicate = NSPredicate(format: "SELF MATCHES %@", usernameRegex)
        
        if !usernamePredicate.evaluate(with: username) {
            usernameError = "Username can only contain letters, numbers, and underscores"
            return
        }
        
        // Check if username starts with a letter
        if let firstChar = username.first, !firstChar.isLetter {
            usernameError = "Username must start with a letter"
            return
        }
    }
    
    private func proceedToPasswordSetup() {
        guard isUsernameValid else { return }
        showingPasswordSetup = true
    }
}

// MARK: - Preview

struct UsernameInputView_Previews: PreviewProvider {
    static var previews: some View {
        UsernameInputView(email: "<EMAIL>")
            .environmentObject(AuthenticationService())
    }
}
