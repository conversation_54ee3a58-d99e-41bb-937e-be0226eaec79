import SwiftUI

/// Main user profile view integrating authentication and profile management
/// 
/// This view serves as the main Profile tab content, handling both
/// authenticated and unauthenticated states with a seamless user experience.
/// 
/// Features:
/// - Authentication state management
/// - User profile display and editing
/// - Food preferences management
/// - Family information settings
/// - App preferences and settings
/// - Accessibility support for all user personas
struct UserProfileView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var profileService: UserProfileService
    @EnvironmentObject private var syncService: UserDataSyncService
    @EnvironmentObject private var appPreferences: AppPreferences
    
    // MARK: - State Properties
    
    @State private var showingEditProfile = false
    @State private var showingFoodPreferences = false
    @State private var showingFamilySettings = false
    @State private var showingNotificationSettings = false
    @State private var showingAppSettings = false

    // Local state for UserDefaults data
    @State private var localDietaryRestrictions: [String] = []
    @State private var localAllergies: [String] = []
    @State private var showingAccountSettings = false
    
    // MARK: - Computed Properties
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }

    private var syncStatusText: String {
        switch syncService.syncState {
        case .synced:
            if let lastSync = syncService.lastSyncDate {
                return "Last synced: \(lastSync.formatted(.relative(presentation: .named)))"
            } else {
                return "Data is up to date"
            }
        case .syncing:
            return "Syncing your data..."
        case .error(let error):
            return "Sync error: \(error.localizedDescription)"
        default:
            return "Ready to sync"
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            Group {
                if authService.isAuthenticated {
                    authenticatedProfileView
                } else {
                    unauthenticatedView
                }
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.large)
            .background(Color(.systemGroupedBackground))
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileView()
        }
        .sheet(isPresented: $showingFoodPreferences) {
            FoodPreferencesView()
        }
        .sheet(isPresented: $showingFamilySettings) {
            FamilyInfoView()
        }
        .sheet(isPresented: $showingNotificationSettings) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showingAppSettings) {
            AppPreferencesView()
        }
        .sheet(isPresented: $showingAccountSettings) {
            DataPrivacyView()
        }
        .onAppear {
            loadLocalData()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("FoodPreferencesUpdated"))) { _ in
            loadLocalData()
        }
    }

    // MARK: - Data Loading

    private func loadLocalData() {
        if let savedRestrictions = UserDefaults.standard.array(forKey: "selectedRestrictions") as? [String] {
            localDietaryRestrictions = savedRestrictions
        }

        if let savedAllergies = UserDefaults.standard.array(forKey: "allergies") as? [String] {
            localAllergies = savedAllergies
        }
    }
    
    // MARK: - Unauthenticated View
    
    private var unauthenticatedView: some View {
        VStack(spacing: 32 * fontScale) {
            // Header Section
            VStack(spacing: 16 * fontScale) {
                Image(systemName: "person.circle")
                    .font(.system(size: 80 * fontScale))
                    .foregroundColor(.secondary)
                    .accessibilityHidden(true)
                
                VStack(spacing: 8 * fontScale) {
                    Text("Welcome to Your Profile")
                        .font(.title2.weight(.semibold))
                        .multilineTextAlignment(.center)
                        .scaleEffect(fontScale)
                    
                    Text("Sign in to access your personalized preferences, family settings, and sync your data across devices")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .scaleEffect(fontScale)
                        .padding(.horizontal, 16)
                }
            }
            
            // Authentication Container
            AuthenticationContainerView()
                .frame(maxHeight: 600)
            
            Spacer()
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 32)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Profile tab. Sign in to access your personalized settings")
    }
    
    // MARK: - Authenticated Profile View

    private var authenticatedProfileView: some View {
        ScrollView {
            VStack(spacing: 20 * fontScale) {
                accountSection
                foodPreferencesSection
                familyInfoSection
                notificationsSection
                appSettingsSection
                dataPrivacySection
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
    }
    
    // MARK: - 👤 Account Section

    private var accountSection: some View {
        ProfileSection(
            title: "Account",
            icon: "person.circle.fill",
            iconColor: .blue
        ) {
            VStack(spacing: 16 * fontScale) {
                // User Avatar and Info
                HStack(spacing: 16) {
                    // Avatar
                    AsyncImage(url: URL(string: authService.currentUser?.photoURL ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Image(systemName: "person.circle.fill")
                            .font(.system(size: 50 * fontScale))
                            .foregroundColor(.secondary)
                    }
                    .frame(width: 50 * fontScale, height: 50 * fontScale)
                    .clipShape(Circle())
                    .accessibilityLabel("User profile picture")

                    // User Info
                    VStack(alignment: .leading, spacing: 4) {
                        Text(authService.currentUser?.bestDisplayName ?? "User")
                            .font(.headline)
                            .scaleEffect(fontScale)

                        if let email = authService.currentUser?.email {
                            Text(email)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .scaleEffect(fontScale)
                        }

                        // Login Status
                        HStack(spacing: 4) {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 8, height: 8)
                            Text("Signed in with \(authService.currentUser?.primaryProvider.displayName ?? "Email")")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .scaleEffect(fontScale)
                        }
                    }

                    Spacer()
                }

                // Sign Out Button
                Button(action: {
                    Task {
                        await authService.signOut()
                    }
                }) {
                    HStack {
                        Image(systemName: "rectangle.portrait.and.arrow.right")
                        Text("Sign Out")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.red.opacity(0.1))
                    .foregroundColor(.red)
                    .cornerRadius(8)
                }
                .scaleEffect(fontScale)
                .accessibilityLabel("Sign out of account")
            }
        }
    }
    
    // MARK: - 🍽️ Food Preferences Section

    private var foodPreferencesSection: some View {
        ProfileSection(
            title: "Food Preferences",
            icon: "leaf.fill",
            iconColor: .green
        ) {
            VStack(spacing: 12 * fontScale) {
                // Strict Exclusions
                PreferenceRow(
                    title: "Strict Exclusions",
                    subtitle: "Foods you never want to use",
                    value: "\(profileService.userProfile?.strictExclusions.count ?? 0) items",
                    icon: "xmark.circle.fill",
                    iconColor: .red,
                    fontScale: fontScale
                ) {
                    showingFoodPreferences = true
                }

                Divider()

                // Dietary Restrictions
                PreferenceRow(
                    title: "Dietary Restrictions",
                    subtitle: "Vegetarian, vegan, gluten-free, etc.",
                    value: "\(localDietaryRestrictions.count) active",
                    icon: "checkmark.seal.fill",
                    iconColor: .green,
                    fontScale: fontScale
                ) {
                    showingFoodPreferences = true
                }

                Divider()

                // Allergies & Intolerances
                PreferenceRow(
                    title: "Allergies & Intolerances",
                    subtitle: "Foods that cause allergic reactions",
                    value: "\(localAllergies.count) allergies",
                    icon: "exclamationmark.triangle.fill",
                    iconColor: .orange,
                    fontScale: fontScale
                ) {
                    showingFoodPreferences = true
                }
            }
        }
    }
    
    // MARK: - 👨‍👩‍👧‍👦 Family Info Section

    private var familyInfoSection: some View {
        ProfileSection(
            title: "Family Information",
            icon: "house.fill",
            iconColor: .blue
        ) {
            VStack(spacing: 12 * fontScale) {
                // Family Size
                PreferenceRow(
                    title: "Family Members",
                    subtitle: "Number of people in household",
                    value: "\(profileService.userProfile?.familyInfo.memberCount ?? 1) members",
                    icon: "person.2.fill",
                    iconColor: .blue,
                    fontScale: fontScale
                ) {
                    showingFamilySettings = true
                }

                Divider()

                // Children Info
                PreferenceRow(
                    title: "Children",
                    subtitle: "Ages and special considerations",
                    value: profileService.userProfile?.familyInfo.hasChildren == true ?
                           "\(profileService.userProfile?.familyInfo.childrenAges.count ?? 0) children" : "No children",
                    icon: "figure.and.child.holdinghands",
                    iconColor: .purple,
                    fontScale: fontScale
                ) {
                    showingFamilySettings = true
                }

                Divider()

                // Special Dietary Needs
                PreferenceRow(
                    title: "Special Dietary Needs",
                    subtitle: "Family members with specific requirements",
                    value: "\(profileService.userProfile?.familyInfo.specialDietaryNeeds.count ?? 0) members",
                    icon: "heart.text.square.fill",
                    iconColor: .pink,
                    fontScale: fontScale
                ) {
                    showingFamilySettings = true
                }
            }
        }
    }
    
    // MARK: - 🔔 Notifications Section

    private var notificationsSection: some View {
        ProfileSection(
            title: "Notifications",
            icon: "bell.fill",
            iconColor: .orange
        ) {
            VStack(spacing: 12 * fontScale) {
                // Ingredient Expiry Reminders
                NotificationToggleRow(
                    title: "Ingredient Expiry Reminders",
                    subtitle: "Get notified before ingredients expire",
                    isOn: .constant(profileService.userProfile?.notificationSettings.ingredientExpiryReminders ?? true),
                    fontScale: fontScale
                ) { newValue in
                    // TODO: Update notification settings
                }

                Divider()

                // Shopping List Reminders
                NotificationToggleRow(
                    title: "Shopping List Reminders",
                    subtitle: "Weekly reminders to check your shopping list",
                    isOn: .constant(profileService.userProfile?.notificationSettings.shoppingListReminders ?? true),
                    fontScale: fontScale
                ) { newValue in
                    // TODO: Update notification settings
                }

                Divider()

                // Recipe Recommendations
                NotificationToggleRow(
                    title: "New Recipe Recommendations",
                    subtitle: "Discover new recipes based on your ingredients",
                    isOn: .constant(profileService.userProfile?.notificationSettings.recipeRecommendations ?? true),
                    fontScale: fontScale
                ) { newValue in
                    showingNotificationSettings = true
                }
            }
        }
    }
    
    // MARK: - ⚙️ App Settings Section

    private var appSettingsSection: some View {
        ProfileSection(
            title: "App Settings",
            icon: "gear",
            iconColor: .gray
        ) {
            VStack(spacing: 12 * fontScale) {
                // Font Size
                PreferenceRow(
                    title: "Font Size",
                    subtitle: "Adjust text size for better readability",
                    value: appPreferences?.fontSize.rawValue ?? "Medium",
                    icon: "textformat.size",
                    iconColor: .indigo,
                    fontScale: fontScale
                ) {
                    showingAppSettings = true
                }

                Divider()

                // Language
                PreferenceRow(
                    title: "Language",
                    subtitle: "Choose your preferred language",
                    value: appPreferences?.language.displayName ?? "English",
                    icon: "globe",
                    iconColor: .blue,
                    fontScale: fontScale
                ) {
                    showingAppSettings = true
                }

                Divider()

                // Measurement Units
                PreferenceRow(
                    title: "Measurement Units",
                    subtitle: "Metric, Imperial, or Mixed units",
                    value: appPreferences?.measurementUnit.rawValue ?? "Metric",
                    icon: "ruler",
                    iconColor: .green,
                    fontScale: fontScale
                ) {
                    showingAppSettings = true
                }
            }
        }
    }

    // MARK: - 🔒 Data & Privacy Section

    private var dataPrivacySection: some View {
        ProfileSection(
            title: "Data & Privacy",
            icon: "lock.shield.fill",
            iconColor: .purple
        ) {
            VStack(spacing: 12 * fontScale) {
                // Data Sync Status
                HStack {
                    Image(systemName: syncService.syncState == .synced ? "checkmark.circle.fill" : "cloud.fill")
                        .foregroundColor(syncService.syncState == .synced ? .green : .blue)
                        .font(.title3)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Data Sync Status")
                            .font(.body.weight(.medium))
                            .scaleEffect(fontScale)

                        Text(syncStatusText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)
                    }

                    Spacer()

                    if syncService.isSyncing {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
                .padding(.vertical, 8)

                Divider()

                // Privacy Policy
                PreferenceRow(
                    title: "Privacy Policy",
                    subtitle: "Learn how we protect your data",
                    value: "",
                    icon: "doc.text.fill",
                    iconColor: .blue,
                    fontScale: fontScale
                ) {
                    // TODO: Show privacy policy
                }

                Divider()

                // Delete Account
                Button(action: {
                    showingAccountSettings = true
                }) {
                    HStack {
                        Image(systemName: "trash.fill")
                            .foregroundColor(.red)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Delete Account")
                                .font(.body.weight(.medium))
                                .foregroundColor(.red)
                                .scaleEffect(fontScale)

                            Text("Permanently delete your account and data")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .scaleEffect(fontScale)
                        }

                        Spacer()
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
                .accessibilityLabel("Delete account permanently")
            }
        }
    }
}

// MARK: - Supporting Components

/// Profile section container with consistent styling
struct ProfileSection<Content: View>: View {
    let title: String
    let icon: String
    let iconColor: Color
    let content: Content

    init(
        title: String,
        icon: String,
        iconColor: Color,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.icon = icon
        self.iconColor = iconColor
        self.content = content()
    }

    var body: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title3.weight(.semibold))
                    .foregroundColor(iconColor)

                Text(title)
                    .font(.title3.weight(.semibold))

                Spacer()
            }

            // Section Content
            VStack(spacing: 0) {
                content
            }
            .padding(16)
            .background(Color(.secondarySystemGroupedBackground))
            .cornerRadius(12)
        }
    }
}

/// Preference row for displaying settings with values
struct PreferenceRow: View {
    let title: String
    let subtitle: String
    let value: String
    let icon: String
    let iconColor: Color
    let fontScale: CGFloat
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // Icon
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor)
                    .frame(width: 24, height: 24)

                // Content
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .scaleEffect(fontScale)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()

                // Value
                Text(value)
                    .font(.caption.weight(.medium))
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)

                // Chevron
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title). \(subtitle). Current value: \(value)")
        .accessibilityHint("Tap to modify this setting")
    }
}

/// Toggle row for notification settings
struct NotificationToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    let fontScale: CGFloat
    let onToggle: (Bool) -> Void

    var body: some View {
        HStack(spacing: 12) {
            // Content
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body.weight(.medium))
                    .scaleEffect(fontScale)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)
            }

            Spacer()

            // Toggle
            Toggle("", isOn: $isOn)
                .scaleEffect(fontScale)
                .onChange(of: isOn) { newValue in
                    onToggle(newValue)
                }
        }
        .padding(.vertical, 8)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title). \(subtitle)")
        .accessibilityValue(isOn ? "On" : "Off")
    }
}

/// Setting row component for navigation to different settings
struct SettingRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let iconColor: Color
    let fontScale: CGFloat
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // Icon
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor)
                    .frame(width: 24 * fontScale, height: 24 * fontScale)

                // Content
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .scaleEffect(fontScale)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()

                // Chevron
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)
            }
            .padding(16)
            .background(Color(.secondarySystemGroupedBackground))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title). \(subtitle)")
        .accessibilityHint("Tap to open \(title.lowercased()) settings")
    }
}

// MARK: - Placeholder Views (to be implemented in future phases)

struct EditProfileView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Edit Profile")
                    .font(.title)
                Text("Coming soon...")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct FoodPreferencesView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Food Preferences")
                    .font(.title)
                Text("Dietary restrictions and allergies management coming soon...")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .navigationTitle("Food Preferences")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct FamilyInfoView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Family Information")
                    .font(.title)
                Text("Family size and dietary needs management coming soon...")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .navigationTitle("Family Information")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct AppPreferencesView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("App Preferences")
                    .font(.title)
                Text("Notifications, language, and accessibility settings coming soon...")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .navigationTitle("App Preferences")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct AccountSettingsView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Account Settings")
                    .font(.title)
                Text("Privacy, data management, and account settings coming soon...")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .navigationTitle("Account Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview

struct UserProfileView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Authenticated state
            UserProfileView()
                .environmentObject({
                    let service = AuthenticationService()
                    // Mock authenticated state
                    return service
                }())
                .environmentObject(UserProfileService())
                .environmentObject(UserDataSyncService(
                    authService: AuthenticationService(),
                    profileService: UserProfileService()
                ))
                .environmentObject(AppPreferences())
                .previewDisplayName("Authenticated")

            // Unauthenticated state
            UserProfileView()
                .environmentObject(AuthenticationService())
                .environmentObject(UserProfileService())
                .environmentObject(UserDataSyncService(
                    authService: AuthenticationService(),
                    profileService: UserProfileService()
                ))
                .environmentObject(AppPreferences())
                .previewDisplayName("Unauthenticated")
        }
    }
}
