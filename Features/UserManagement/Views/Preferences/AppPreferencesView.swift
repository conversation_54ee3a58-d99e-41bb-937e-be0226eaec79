import SwiftUI

/// Complete app preferences management interface
/// 
/// Allows users to customize their app experience including
/// font size, language, measurement units, and accessibility options.
/// 
/// Features:
/// - Font size adjustment (Small, Medium, Large, Extra Large)
/// - Language selection with localization support
/// - Measurement unit preferences (Metric, Imperial, Mixed)
/// - Accessibility enhancements
/// - Real-time preview of changes
struct AppPreferencesView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var profileService: UserProfileService
    @EnvironmentObject private var appPreferences: AppPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var selectedFontSize: FontSize = .medium
    @State private var selectedLanguage: Language = .english
    @State private var selectedMeasurementUnit: MeasurementUnit = .metric
    
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    // MARK: - Computed Properties
    
    private var fontScale: CGFloat {
        selectedFontSize.scaleFactor
    }
    
    private var hasChanges: Bool {
        guard let preferences = appPreferences else { return false }
        return selectedFontSize != preferences.fontSize ||
               selectedLanguage != preferences.language ||
               selectedMeasurementUnit != preferences.measurementUnit
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24 * fontScale) {
                    fontSizeSection
                    languageSection
                    measurementUnitSection
                    previewSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("App Preferences")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .scaleEffect(fontScale)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(!hasChanges || isLoading)
                    .scaleEffect(fontScale)
                }
            }
            .onAppear {
                loadCurrentPreferences()
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
    }
    
    // MARK: - Font Size Section
    
    private var fontSizeSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Font Size")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Choose the text size that's most comfortable for you")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Font Size Options
            VStack(spacing: 12) {
                ForEach(FontSize.allCases, id: \.self) { fontSize in
                    FontSizeOption(
                        fontSize: fontSize,
                        isSelected: selectedFontSize == fontSize,
                        currentScale: fontScale
                    ) {
                        selectedFontSize = fontSize
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Language Section
    
    private var languageSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Language")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Select your preferred language")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Language Options
            VStack(spacing: 8) {
                ForEach(Language.allCases, id: \.self) { language in
                    LanguageOption(
                        language: language,
                        isSelected: selectedLanguage == language,
                        fontScale: fontScale
                    ) {
                        selectedLanguage = language
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Measurement Unit Section
    
    private var measurementUnitSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Measurement Units")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Choose your preferred units for recipes and ingredients")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Unit Options
            VStack(spacing: 12) {
                ForEach(MeasurementUnit.allCases, id: \.self) { unit in
                    MeasurementUnitOption(
                        unit: unit,
                        isSelected: selectedMeasurementUnit == unit,
                        fontScale: fontScale
                    ) {
                        selectedMeasurementUnit = unit
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Preview Section
    
    private var previewSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Preview")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("See how your settings will look")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Preview Content
            VStack(spacing: 12) {
                // Recipe Title Preview
                HStack {
                    Text("Recipe Title")
                        .font(.title3.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Spacer()
                }
                
                // Ingredient Preview
                HStack {
                    Text("• 2 \(selectedMeasurementUnit.weightExample) flour")
                        .font(.body)
                        .scaleEffect(fontScale)
                    
                    Spacer()
                }
                
                HStack {
                    Text("• 500 \(selectedMeasurementUnit.volumeExample) milk")
                        .font(.body)
                        .scaleEffect(fontScale)
                    
                    Spacer()
                }
                
                // Language Preview
                HStack {
                    Text(selectedLanguage.sampleText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                    
                    Spacer()
                }
            }
            .padding(12)
            .background(Color(.tertiarySystemGroupedBackground))
            .cornerRadius(8)
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
}

// MARK: - Private Implementation

private extension AppPreferencesView {

    func loadCurrentPreferences() {
        guard let preferences = appPreferences else { return }

        selectedFontSize = preferences.fontSize
        selectedLanguage = preferences.language
        selectedMeasurementUnit = preferences.measurementUnit
    }

    func saveChanges() async {
        isLoading = true
        errorMessage = nil

        do {
            guard let userId = profileService.userProfile?.id else {
                throw NSError(domain: "AppPreferences", code: 1, userInfo: [NSLocalizedDescriptionKey: "User not found"])
            }

            let preferences = AppPreferences(
                fontSize: selectedFontSize,
                language: selectedLanguage,
                measurementUnit: selectedMeasurementUnit
            )

            try await profileService.updateAppPreferences(userId: userId, preferences: preferences)

            // Update local preferences immediately
            await MainActor.run {
                appPreferences?.fontSize = selectedFontSize
                appPreferences?.language = selectedLanguage
                appPreferences?.measurementUnit = selectedMeasurementUnit
                dismiss()
            }

        } catch {
            await MainActor.run {
                errorMessage = "Failed to save preferences: \(error.localizedDescription)"
            }
        }

        isLoading = false
    }
}

// MARK: - Supporting Components

/// Font size selection option
struct FontSizeOption: View {
    let fontSize: FontSize
    let isSelected: Bool
    let currentScale: CGFloat
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(fontSize.displayName)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .scaleEffect(currentScale)

                    Text("Sample text at this size")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontSize.scaleFactor)
                }

                Spacer()

                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .font(.title3)
                    .scaleEffect(currentScale)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(fontSize.displayName) font size. \(isSelected ? "Selected" : "Not selected")")
    }
}

/// Language selection option
struct LanguageOption: View {
    let language: Language
    let isSelected: Bool
    let fontScale: CGFloat
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(language.displayName)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .scaleEffect(fontScale)

                    Text(language.nativeName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()

                if !language.isAvailable {
                    Text("Coming Soon")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(4)
                        .scaleEffect(fontScale)
                }

                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .font(.title3)
                    .scaleEffect(fontScale)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!language.isAvailable)
        .opacity(language.isAvailable ? 1.0 : 0.6)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(language.displayName). \(isSelected ? "Selected" : "Not selected")")
    }
}

/// Measurement unit selection option
struct MeasurementUnitOption: View {
    let unit: MeasurementUnit
    let isSelected: Bool
    let fontScale: CGFloat
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(unit.displayName)
                            .font(.body.weight(.medium))
                            .foregroundColor(.primary)
                            .scaleEffect(fontScale)

                        Text(unit.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)
                    }

                    Spacer()

                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .blue : .secondary)
                        .font(.title3)
                        .scaleEffect(fontScale)
                }

                // Examples
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Weight: \(unit.weightExample)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)

                        Text("Volume: \(unit.volumeExample)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)

                        Text("Temperature: \(unit.temperatureExample)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)
                    }

                    Spacer()
                }
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(unit.displayName). \(unit.description). \(isSelected ? "Selected" : "Not selected")")
    }
}

// MARK: - Extensions

extension Language {
    var sampleText: String {
        switch self {
        case .english: return "This is how text will appear"
        case .spanish: return "Así es como aparecerá el texto"
        case .french: return "C'est ainsi que le texte apparaîtra"
        case .german: return "So wird der Text erscheinen"
        case .chinese: return "文本将这样显示"
        case .japanese: return "テキストはこのように表示されます"
        }
    }

    var isAvailable: Bool {
        switch self {
        case .english: return true
        case .spanish, .french, .german, .chinese, .japanese: return false // Coming soon
        }
    }
}

extension MeasurementUnit {
    var weightExample: String {
        switch self {
        case .metric: return "grams (g)"
        case .imperial: return "ounces (oz)"
        case .mixed: return "grams/pounds"
        }
    }

    var volumeExample: String {
        switch self {
        case .metric: return "milliliters (ml)"
        case .imperial: return "fluid ounces (fl oz)"
        case .mixed: return "cups/liters"
        }
    }

    var temperatureExample: String {
        switch self {
        case .metric: return "Celsius (°C)"
        case .imperial: return "Fahrenheit (°F)"
        case .mixed: return "Celsius (°C)"
        }
    }

    var description: String {
        switch self {
        case .metric: return "Grams, liters, Celsius"
        case .imperial: return "Ounces, cups, Fahrenheit"
        case .mixed: return "Best of both systems"
        }
    }
}
