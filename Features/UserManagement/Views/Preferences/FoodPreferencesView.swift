import SwiftUI

/// Complete food preferences management interface
/// 
/// Allows users to manage their dietary restrictions, allergies,
/// and strict food exclusions with an intuitive interface.
/// 
/// Features:
/// - Strict exclusions management
/// - Dietary restrictions selection
/// - Allergy management
/// - Real-time validation
/// - Accessibility support
struct FoodPreferencesView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var profileService: UserProfileService
    @EnvironmentObject private var appPreferences: AppPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties

    @State private var strictExclusions: [String] = []
    @State private var selectedDietaryRestrictions: Set<DietaryRestriction> = []
    @State private var allergies: [String] = []

    @State private var newExclusion = ""
    @State private var newAllergy = ""
    @State private var showingAddExclusion = false
    @State private var showingAddAllergy = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var hasInitialized = false
    
    // MARK: - Computed Properties
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }
    
    private var hasChanges: Bool {
        guard let profile = profileService.userProfile else { return false }
        return strictExclusions != profile.strictExclusions ||
               selectedDietaryRestrictions != Set(profile.dietaryRestrictions) ||
               allergies != profile.allergies
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24 * fontScale) {
                    // Error message display
                    if let errorMessage = errorMessage {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.red)
                                .scaleEffect(fontScale)
                            Spacer()
                            Button("Dismiss") {
                                self.errorMessage = nil
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal, 20)
                    }

                    strictExclusionsSection
                    dietaryRestrictionsSection
                    allergiesSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Food Preferences")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .scaleEffect(fontScale)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(!hasChanges || isLoading)
                    .scaleEffect(fontScale)
                }
            }
            .onAppear {
                // Only load preferences on first appearance to avoid overwriting user changes
                if !hasInitialized {
                    loadCurrentPreferences()
                    hasInitialized = true
                }
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
    }
    
    // MARK: - Strict Exclusions Section
    
    private var strictExclusionsSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Strict Exclusions")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Foods you never want to use in any recipe")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                Button(action: {
                    showingAddExclusion = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(.red)
                }
                .scaleEffect(fontScale)
                .accessibilityLabel("Add strict exclusion")
            }
            
            // Exclusions List
            if strictExclusions.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "xmark.circle")
                        .font(.title)
                        .foregroundColor(.secondary)
                    
                    Text("No strict exclusions")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 32)
                .background(Color(.tertiarySystemGroupedBackground))
                .cornerRadius(12)
            } else {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 120), spacing: 8)
                ], spacing: 8) {
                    ForEach(strictExclusions, id: \.self) { exclusion in
                        ExclusionTag(
                            text: exclusion,
                            color: .red,
                            fontScale: fontScale
                        ) {
                            strictExclusions.removeAll { $0 == exclusion }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
        .sheet(isPresented: $showingAddExclusion) {
            AddItemSheet(
                title: "Add Strict Exclusion",
                placeholder: "Enter food to exclude (e.g., pork, alcohol)",
                text: $newExclusion
            ) {
                let trimmedExclusion = newExclusion.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
                if !trimmedExclusion.isEmpty && !strictExclusions.contains(trimmedExclusion) {
                    strictExclusions.append(trimmedExclusion)
                    newExclusion = ""
                    // Provide haptic feedback for successful addition
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }
            }
        }
    }
    
    // MARK: - Dietary Restrictions Section
    
    private var dietaryRestrictionsSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Dietary Restrictions")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Select all that apply to your diet")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Restrictions Grid
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 150), spacing: 12)
            ], spacing: 12) {
                ForEach(DietaryRestriction.allCases, id: \.self) { restriction in
                    DietaryRestrictionCard(
                        restriction: restriction,
                        isSelected: selectedDietaryRestrictions.contains(restriction),
                        fontScale: fontScale
                    ) {
                        if selectedDietaryRestrictions.contains(restriction) {
                            selectedDietaryRestrictions.remove(restriction)
                        } else {
                            selectedDietaryRestrictions.insert(restriction)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Allergies Section
    
    private var allergiesSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Allergies & Intolerances")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Foods that cause allergic reactions or intolerances")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                Button(action: {
                    showingAddAllergy = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(.orange)
                }
                .scaleEffect(fontScale)
                .accessibilityLabel("Add allergy")
            }
            
            // Allergies List
            if allergies.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.title)
                        .foregroundColor(.secondary)
                    
                    Text("No allergies recorded")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 32)
                .background(Color(.tertiarySystemGroupedBackground))
                .cornerRadius(12)
            } else {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 120), spacing: 8)
                ], spacing: 8) {
                    ForEach(allergies, id: \.self) { allergy in
                        ExclusionTag(
                            text: allergy,
                            color: .orange,
                            fontScale: fontScale
                        ) {
                            allergies.removeAll { $0 == allergy }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
        .sheet(isPresented: $showingAddAllergy) {
            AddItemSheet(
                title: "Add Allergy",
                placeholder: "Enter allergen (e.g., peanuts, shellfish)",
                text: $newAllergy
            ) {
                let trimmedAllergy = newAllergy.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
                if !trimmedAllergy.isEmpty && !allergies.contains(trimmedAllergy) {
                    allergies.append(trimmedAllergy)
                    newAllergy = ""
                    // Provide haptic feedback for successful addition
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }
            }
        }
    }
}

// MARK: - Private Implementation

private extension FoodPreferencesView {

    func loadCurrentPreferences() {
        guard let profile = profileService.userProfile else { return }

        strictExclusions = profile.strictExclusions
        selectedDietaryRestrictions = Set(profile.dietaryRestrictions)
        allergies = profile.allergies
    }

    func saveChanges() async {
        isLoading = true
        errorMessage = nil

        do {
            guard let userId = profileService.userProfile?.id else {
                throw NSError(domain: "FoodPreferences", code: 1, userInfo: [NSLocalizedDescriptionKey: "User not found"])
            }

            // Update food preferences
            try await profileService.updateFoodPreferences(
                userId: userId,
                strictExclusions: strictExclusions,
                dietaryRestrictions: Array(selectedDietaryRestrictions),
                allergies: allergies
            )

            // Provide success feedback
            await MainActor.run {
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)

                // Dismiss on success
                dismiss()
            }

        } catch {
            await MainActor.run {
                errorMessage = "Failed to save preferences: \(error.localizedDescription)"
                let errorFeedback = UINotificationFeedbackGenerator()
                errorFeedback.notificationOccurred(.error)
            }
        }

        isLoading = false
    }
}

// MARK: - Supporting Components

/// Tag component for exclusions and allergies
struct ExclusionTag: View {
    let text: String
    let color: Color
    let fontScale: CGFloat
    let onRemove: () -> Void

    var body: some View {
        HStack(spacing: 6) {
            Text(text.capitalized)
                .font(.caption.weight(.medium))
                .scaleEffect(fontScale)

            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.white)
            }
            .accessibilityLabel("Remove \(text)")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(color)
        .foregroundColor(.white)
        .cornerRadius(16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(text). Tap to remove")
    }
}

/// Card component for dietary restrictions
struct DietaryRestrictionCard: View {
    let restriction: DietaryRestriction
    let isSelected: Bool
    let fontScale: CGFloat
    let onToggle: () -> Void

    var body: some View {
        Button(action: onToggle) {
            VStack(spacing: 8) {
                Image(systemName: restriction.iconName)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : restriction.color)

                Text(restriction.displayName)
                    .font(.caption.weight(.medium))
                    .multilineTextAlignment(.center)
                    .foregroundColor(isSelected ? .white : .primary)
                    .scaleEffect(fontScale)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(isSelected ? restriction.color : Color(.tertiarySystemGroupedBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(restriction.color, lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(restriction.displayName). \(isSelected ? "Selected" : "Not selected")")
        .accessibilityHint("Tap to \(isSelected ? "deselect" : "select") this dietary restriction")
    }
}

/// Sheet for adding new items
struct AddItemSheet: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let onAdd: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Item Name")
                        .font(.headline)

                    TextField(placeholder, text: $text)
                        .textFieldStyle(.roundedBorder)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                }

                Spacer()
            }
            .padding(20)
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        onAdd()
                        // Delay dismiss to ensure state update completes
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            dismiss()
                        }
                    }
                    .disabled(text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}

// MARK: - DietaryRestriction Extensions

extension DietaryRestriction {
    var iconName: String {
        switch self {
        case .vegetarian: return "leaf.fill"
        case .vegan: return "leaf.circle.fill"
        case .glutenFree: return "g.circle.fill"
        case .dairyFree: return "drop.circle.fill"
        case .nutFree: return "exclamationmark.triangle.fill"
        case .keto: return "flame.fill"
        case .paleo: return "figure.walk"
        case .lowCarb: return "minus.circle.fill"
        case .lowSodium: return "drop.triangle.fill"
        case .kosher: return "star.fill"
        }
    }

    var color: Color {
        switch self {
        case .vegetarian: return .green
        case .vegan: return .mint
        case .glutenFree: return .orange
        case .dairyFree: return .blue
        case .nutFree: return .red
        case .keto: return .purple
        case .paleo: return .brown
        case .lowCarb: return .indigo
        case .lowSodium: return .cyan
        case .kosher: return .yellow
        }
    }
}
