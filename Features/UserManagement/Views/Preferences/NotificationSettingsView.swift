import SwiftUI
import UserNotifications

/// Complete notification settings management interface
/// 
/// Allows users to control all types of notifications including
/// ingredient expiry reminders, shopping list alerts, and recipe recommendations.
/// 
/// Features:
/// - Ingredient expiry notifications with timing options
/// - Shopping list reminders with frequency settings
/// - Recipe recommendation notifications
/// - System notification permission management
/// - Accessibility support
struct NotificationSettingsView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var profileService: UserProfileService
    @EnvironmentObject private var appPreferences: AppPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var ingredientExpiryReminders: Bool = true
    @State private var expiryReminderDays: Int = 2
    @State private var shoppingListReminders: Bool = true
    @State private var shoppingReminderFrequency: ShoppingReminderFrequency = .weekly
    @State private var recipeRecommendations: Bool = true
    @State private var recommendationFrequency: RecommendationFrequency = .daily
    
    @State private var systemNotificationsEnabled: Bool = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingPermissionAlert = false
    
    // MARK: - Computed Properties
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }
    
    private var hasChanges: Bool {
        guard let profile = profileService.userProfile else { return false }
        let settings = profile.notificationSettings
        return ingredientExpiryReminders != settings.ingredientExpiryReminders ||
               expiryReminderDays != settings.expiryReminderDays ||
               shoppingListReminders != settings.shoppingListReminders ||
               shoppingReminderFrequency != settings.shoppingReminderFrequency ||
               recipeRecommendations != settings.recipeRecommendations ||
               recommendationFrequency != settings.recommendationFrequency
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24 * fontScale) {
                    systemPermissionSection
                    ingredientExpirySection
                    shoppingListSection
                    recipeRecommendationSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .scaleEffect(fontScale)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(!hasChanges || isLoading)
                    .scaleEffect(fontScale)
                }
            }
            .onAppear {
                loadCurrentSettings()
                checkNotificationPermission()
            }
            .alert("Notification Permission Required", isPresented: $showingPermissionAlert) {
                Button("Settings") {
                    openAppSettings()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("To receive notifications, please enable them in Settings > Notifications > Ingredient Scanner")
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
    }
    
    // MARK: - System Permission Section
    
    private var systemPermissionSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("System Notifications")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Allow the app to send you notifications")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Permission Status
            HStack {
                Image(systemName: systemNotificationsEnabled ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(systemNotificationsEnabled ? .green : .red)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(systemNotificationsEnabled ? "Notifications Enabled" : "Notifications Disabled")
                        .font(.body.weight(.medium))
                        .scaleEffect(fontScale)
                    
                    Text(systemNotificationsEnabled ? 
                         "You'll receive notifications based on your preferences below" :
                         "Enable notifications in Settings to receive alerts")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                if !systemNotificationsEnabled {
                    Button("Enable") {
                        requestNotificationPermission()
                    }
                    .buttonStyle(.bordered)
                    .scaleEffect(fontScale)
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Ingredient Expiry Section
    
    private var ingredientExpirySection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Ingredient Expiry Reminders")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Get notified before your ingredients expire")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                Toggle("", isOn: $ingredientExpiryReminders)
                    .scaleEffect(fontScale)
            }
            
            if ingredientExpiryReminders {
                VStack(spacing: 12) {
                    Divider()
                    
                    // Reminder timing
                    HStack {
                        Text("Remind me")
                            .font(.body)
                            .scaleEffect(fontScale)
                        
                        Spacer()
                        
                        Picker("Days before expiry", selection: $expiryReminderDays) {
                            Text("1 day before").tag(1)
                            Text("2 days before").tag(2)
                            Text("3 days before").tag(3)
                            Text("1 week before").tag(7)
                        }
                        .pickerStyle(.menu)
                        .scaleEffect(fontScale)
                    }
                    
                    // Example
                    HStack {
                        Image(systemName: "info.circle")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Text("Example: Milk expires on Friday → Notification on \(exampleExpiryDate)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)
                        
                        Spacer()
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Shopping List Section
    
    private var shoppingListSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Shopping List Reminders")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Regular reminders to check your shopping list")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                Toggle("", isOn: $shoppingListReminders)
                    .scaleEffect(fontScale)
            }
            
            if shoppingListReminders {
                VStack(spacing: 12) {
                    Divider()
                    
                    // Frequency selection
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Frequency")
                            .font(.body.weight(.medium))
                            .scaleEffect(fontScale)
                        
                        ForEach(ShoppingReminderFrequency.allCases, id: \.self) { frequency in
                            HStack {
                                Button(action: {
                                    shoppingReminderFrequency = frequency
                                }) {
                                    HStack {
                                        Image(systemName: shoppingReminderFrequency == frequency ? "checkmark.circle.fill" : "circle")
                                            .foregroundColor(shoppingReminderFrequency == frequency ? .blue : .secondary)
                                        
                                        VStack(alignment: .leading, spacing: 2) {
                                            Text(frequency.displayName)
                                                .font(.body)
                                                .foregroundColor(.primary)
                                                .scaleEffect(fontScale)
                                            
                                            Text(frequency.description)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                                .scaleEffect(fontScale)
                                        }
                                        
                                        Spacer()
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }

    // MARK: - Recipe Recommendation Section

    private var recipeRecommendationSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Recipe Recommendations")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)

                    Text("Discover new recipes based on your ingredients")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()

                Toggle("", isOn: $recipeRecommendations)
                    .scaleEffect(fontScale)
            }

            if recipeRecommendations {
                VStack(spacing: 12) {
                    Divider()

                    // Frequency selection
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Frequency")
                            .font(.body.weight(.medium))
                            .scaleEffect(fontScale)

                        ForEach(RecommendationFrequency.allCases, id: \.self) { frequency in
                            HStack {
                                Button(action: {
                                    recommendationFrequency = frequency
                                }) {
                                    HStack {
                                        Image(systemName: recommendationFrequency == frequency ? "checkmark.circle.fill" : "circle")
                                            .foregroundColor(recommendationFrequency == frequency ? .blue : .secondary)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text(frequency.displayName)
                                                .font(.body)
                                                .foregroundColor(.primary)
                                                .scaleEffect(fontScale)

                                            Text(frequency.description)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                                .scaleEffect(fontScale)
                                        }

                                        Spacer()
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
}

// MARK: - Private Implementation

private extension NotificationSettingsView {

    var exampleExpiryDate: String {
        let calendar = Calendar.current
        let today = Date()
        let expiryDate = calendar.date(byAdding: .day, value: 5, to: today) ?? today
        let reminderDate = calendar.date(byAdding: .day, value: -expiryReminderDays, to: expiryDate) ?? today

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return formatter.string(from: reminderDate)
    }

    func loadCurrentSettings() {
        guard let profile = profileService.userProfile else { return }
        let settings = profile.notificationSettings

        ingredientExpiryReminders = settings.ingredientExpiryReminders
        expiryReminderDays = settings.expiryReminderDays
        shoppingListReminders = settings.shoppingListReminders
        shoppingReminderFrequency = settings.shoppingReminderFrequency
        recipeRecommendations = settings.recipeRecommendations
        recommendationFrequency = settings.recommendationFrequency
    }

    func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                systemNotificationsEnabled = settings.authorizationStatus == .authorized
            }
        }
    }

    func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    systemNotificationsEnabled = true
                } else {
                    showingPermissionAlert = true
                }
            }
        }
    }

    func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }

    func saveChanges() async {
        isLoading = true
        errorMessage = nil

        do {
            guard let userId = profileService.userProfile?.id else {
                throw NSError(domain: "NotificationSettings", code: 1, userInfo: [NSLocalizedDescriptionKey: "User not found"])
            }

            let settings = NotificationSettings(
                ingredientExpiryReminders: ingredientExpiryReminders,
                expiryReminderDays: expiryReminderDays,
                shoppingListReminders: shoppingListReminders,
                shoppingReminderFrequency: shoppingReminderFrequency,
                recipeRecommendations: recipeRecommendations,
                recommendationFrequency: recommendationFrequency
            )

            try await profileService.updateNotificationSettings(userId: userId, settings: settings)

            // Dismiss on success
            await MainActor.run {
                dismiss()
            }

        } catch {
            await MainActor.run {
                errorMessage = "Failed to save notification settings: \(error.localizedDescription)"
            }
        }

        isLoading = false
    }
}

// MARK: - Supporting Enums

enum ShoppingReminderFrequency: String, CaseIterable, Codable {
    case daily = "daily"
    case weekly = "weekly"
    case biweekly = "biweekly"

    var displayName: String {
        switch self {
        case .daily: return "Daily"
        case .weekly: return "Weekly"
        case .biweekly: return "Bi-weekly"
        }
    }

    var description: String {
        switch self {
        case .daily: return "Every day at 6 PM"
        case .weekly: return "Every Sunday at 6 PM"
        case .biweekly: return "Every other Sunday at 6 PM"
        }
    }
}

enum RecommendationFrequency: String, CaseIterable, Codable {
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"

    var displayName: String {
        switch self {
        case .daily: return "Daily"
        case .weekly: return "Weekly"
        case .monthly: return "Monthly"
        }
    }

    var description: String {
        switch self {
        case .daily: return "New recipe suggestions every day"
        case .weekly: return "Weekly recipe roundup"
        case .monthly: return "Monthly featured recipes"
        }
    }
}
