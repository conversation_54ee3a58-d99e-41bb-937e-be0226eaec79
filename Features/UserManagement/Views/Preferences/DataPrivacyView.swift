import SwiftUI

/// Complete data and privacy management interface
/// 
/// Provides users with full control over their data including
/// sync status, privacy settings, data export, and account deletion.
/// 
/// Features:
/// - Real-time data sync status monitoring
/// - Privacy policy and terms access
/// - Data export functionality
/// - Account deletion with confirmation
/// - Data usage statistics
/// - Accessibility support
struct DataPrivacyView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var profileService: UserProfileService
    @EnvironmentObject private var syncService: UserDataSyncService
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var appPreferences: AppPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var showingDeleteConfirmation = false
    @State private var showingDataExport = false
    @State private var showingPrivacyPolicy = false
    @State private var isDeleting = false
    @State private var errorMessage: String?
    @State private var dataStats: DataStats?
    
    // MARK: - Computed Properties
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }
    
    private var syncStatusText: String {
        switch syncService.syncState {
        case .synced:
            if let lastSync = syncService.lastSyncDate {
                return "Last synced: \(lastSync.formatted(.relative(presentation: .named)))"
            } else {
                return "Data is up to date"
            }
        case .syncing:
            return "Syncing your data..."
        case .error(let error):
            return "Sync error: \(error.localizedDescription)"
        default:
            return "Ready to sync"
        }
    }
    
    private var syncStatusColor: Color {
        switch syncService.syncState {
        case .synced: return .green
        case .syncing: return .blue
        case .error: return .red
        default: return .gray
        }
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24 * fontScale) {
                    dataSyncSection
                    dataUsageSection
                    privacySection
                    dangerZoneSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Data & Privacy")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .scaleEffect(fontScale)
                }
            }
            .onAppear {
                loadDataStats()
            }
            .alert("Delete Account", isPresented: $showingDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    Task {
                        await deleteAccount()
                    }
                }
            } message: {
                Text("This action cannot be undone. All your data will be permanently deleted.")
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
            .sheet(isPresented: $showingDataExport) {
                DataExportView()
            }
            .sheet(isPresented: $showingPrivacyPolicy) {
                PrivacyPolicyView()
            }
        }
    }
    
    // MARK: - Data Sync Section
    
    private var dataSyncSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Data Synchronization")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Your data is automatically synced across all your devices")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Sync Status
            HStack {
                Image(systemName: syncService.syncState == .synced ? "checkmark.circle.fill" : 
                      syncService.syncState == .syncing ? "arrow.triangle.2.circlepath" : "cloud.fill")
                    .foregroundColor(syncStatusColor)
                    .font(.title2)
                    .symbolEffect(.pulse, isActive: syncService.isSyncing)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Sync Status")
                        .font(.body.weight(.medium))
                        .scaleEffect(fontScale)
                    
                    Text(syncStatusText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                if syncService.isSyncing {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Button("Sync Now") {
                        Task {
                            await syncService.forceSyncUserData()
                        }
                    }
                    .buttonStyle(.bordered)
                    .scaleEffect(fontScale)
                }
            }
            
            Divider()
            
            // Sync Settings
            VStack(spacing: 12) {
                HStack {
                    Text("What gets synced:")
                        .font(.body.weight(.medium))
                        .scaleEffect(fontScale)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 6) {
                    SyncItemRow(title: "Food preferences & allergies", isEnabled: true, fontScale: fontScale)
                    SyncItemRow(title: "Family information", isEnabled: true, fontScale: fontScale)
                    SyncItemRow(title: "App settings", isEnabled: true, fontScale: fontScale)
                    SyncItemRow(title: "Notification preferences", isEnabled: true, fontScale: fontScale)
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Data Usage Section
    
    private var dataUsageSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Data Usage")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Overview of your stored data")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                Button("Export Data") {
                    showingDataExport = true
                }
                .buttonStyle(.bordered)
                .scaleEffect(fontScale)
            }
            
            // Data Statistics
            if let stats = dataStats {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    DataStatCard(
                        title: "Food Preferences",
                        value: "\(stats.foodPreferencesCount)",
                        icon: "leaf.fill",
                        color: .green,
                        fontScale: fontScale
                    )
                    
                    DataStatCard(
                        title: "Family Members",
                        value: "\(stats.familyMembersCount)",
                        icon: "person.2.fill",
                        color: .blue,
                        fontScale: fontScale
                    )
                    
                    DataStatCard(
                        title: "Account Age",
                        value: stats.accountAgeText,
                        icon: "calendar.circle.fill",
                        color: .purple,
                        fontScale: fontScale
                    )
                    
                    DataStatCard(
                        title: "Data Size",
                        value: stats.dataSizeText,
                        icon: "externaldrive.fill",
                        color: .orange,
                        fontScale: fontScale
                    )
                }
            } else {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    
                    Text("Loading data statistics...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Privacy Section
    
    private var privacySection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Privacy & Legal")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Learn about how we protect your data")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Privacy Links
            VStack(spacing: 8) {
                PrivacyLinkRow(
                    title: "Privacy Policy",
                    subtitle: "How we collect, use, and protect your data",
                    icon: "doc.text.fill",
                    fontScale: fontScale
                ) {
                    showingPrivacyPolicy = true
                }
                
                PrivacyLinkRow(
                    title: "Terms of Service",
                    subtitle: "Terms and conditions for using the app",
                    icon: "doc.plaintext.fill",
                    fontScale: fontScale
                ) {
                    // TODO: Show terms of service
                }
                
                PrivacyLinkRow(
                    title: "Data Processing",
                    subtitle: "Learn how your data is processed and stored",
                    icon: "server.rack",
                    fontScale: fontScale
                ) {
                    // TODO: Show data processing info
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }

    // MARK: - Danger Zone Section

    private var dangerZoneSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Danger Zone")
                        .font(.title2.weight(.semibold))
                        .foregroundColor(.red)
                        .scaleEffect(fontScale)

                    Text("Irreversible actions that affect your account")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()
            }

            // Danger Actions
            VStack(spacing: 12) {
                // Clear Local Data
                Button(action: {
                    Task {
                        await syncService.clearLocalUserData()
                    }
                }) {
                    HStack {
                        Image(systemName: "trash.circle.fill")
                            .foregroundColor(.orange)
                            .font(.title3)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Clear Local Data")
                                .font(.body.weight(.medium))
                                .foregroundColor(.orange)
                                .scaleEffect(fontScale)

                            Text("Remove all data from this device (can be re-synced)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .scaleEffect(fontScale)
                        }

                        Spacer()
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())

                Divider()

                // Delete Account
                Button(action: {
                    showingDeleteConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "person.crop.circle.badge.xmark.fill")
                            .foregroundColor(.red)
                            .font(.title3)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Delete Account")
                                .font(.body.weight(.medium))
                                .foregroundColor(.red)
                                .scaleEffect(fontScale)

                            Text("Permanently delete your account and all data")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .scaleEffect(fontScale)
                        }

                        Spacer()

                        if isDeleting {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(isDeleting)
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - Private Implementation

private extension DataPrivacyView {

    func loadDataStats() {
        guard let profile = profileService.userProfile else { return }

        let foodPreferencesCount = profile.strictExclusions.count +
                                 profile.dietaryRestrictions.count +
                                 profile.allergies.count

        let accountAge = Date().timeIntervalSince(profile.createdAt)
        let accountAgeDays = Int(accountAge / 86400) // seconds to days

        dataStats = DataStats(
            foodPreferencesCount: foodPreferencesCount,
            familyMembersCount: profile.familyInfo.memberCount,
            accountAgeDays: accountAgeDays,
            estimatedDataSizeKB: 15 // Rough estimate
        )
    }

    func deleteAccount() async {
        isDeleting = true
        errorMessage = nil

        do {
            // Delete user data from Firestore
            if let userId = profileService.userProfile?.id {
                try await profileService.deleteUserProfile(userId: userId)
            }

            // Delete Firebase Auth account
            try await authService.deleteAccount()

            // Clear local data
            await syncService.clearLocalUserData()

            // Dismiss and sign out
            await MainActor.run {
                dismiss()
            }

        } catch {
            await MainActor.run {
                errorMessage = "Failed to delete account: \(error.localizedDescription)"
            }
        }

        isDeleting = false
    }
}

// MARK: - Supporting Components

/// Row for sync items
struct SyncItemRow: View {
    let title: String
    let isEnabled: Bool
    let fontScale: CGFloat

    var body: some View {
        HStack {
            Image(systemName: isEnabled ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(isEnabled ? .green : .red)
                .font(.caption)

            Text(title)
                .font(.caption)
                .scaleEffect(fontScale)

            Spacer()
        }
    }
}

/// Card for data statistics
struct DataStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let fontScale: CGFloat

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .scaleEffect(fontScale)

            Text(value)
                .font(.headline.weight(.bold))
                .scaleEffect(fontScale)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .scaleEffect(fontScale)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.tertiarySystemGroupedBackground))
        .cornerRadius(8)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title): \(value)")
    }
}

/// Row for privacy links
struct PrivacyLinkRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let fontScale: CGFloat
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body.weight(.medium))
                        .foregroundColor(.primary)
                        .scaleEffect(fontScale)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title). \(subtitle)")
    }
}

// MARK: - Supporting Types

struct DataStats {
    let foodPreferencesCount: Int
    let familyMembersCount: Int
    let accountAgeDays: Int
    let estimatedDataSizeKB: Int

    var accountAgeText: String {
        if accountAgeDays < 30 {
            return "\(accountAgeDays) days"
        } else if accountAgeDays < 365 {
            return "\(accountAgeDays / 30) months"
        } else {
            return "\(accountAgeDays / 365) years"
        }
    }

    var dataSizeText: String {
        if estimatedDataSizeKB < 1024 {
            return "\(estimatedDataSizeKB) KB"
        } else {
            return "\(estimatedDataSizeKB / 1024) MB"
        }
    }
}

// MARK: - Placeholder Views

struct DataExportView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Data Export")
                    .font(.title)
                Text("Export functionality coming soon...")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            .navigationTitle("Export Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Privacy Policy")
                        .font(.title.weight(.bold))

                    Text("Last updated: \(Date().formatted(date: .abbreviated, time: .omitted))")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("Your privacy is important to us. This privacy policy explains how we collect, use, and protect your information when you use our app.")
                        .font(.body)

                    Text("Information We Collect")
                        .font(.headline)

                    Text("• Account information (email, name)\n• Food preferences and dietary restrictions\n• Family information for recipe scaling\n• App usage data for improving our service")
                        .font(.body)

                    Text("How We Use Your Information")
                        .font(.headline)

                    Text("• To provide personalized recipe recommendations\n• To sync your data across devices\n• To improve our app and services\n• To send you notifications (if enabled)")
                        .font(.body)

                    Text("Data Security")
                        .font(.headline)

                    Text("We use industry-standard security measures to protect your data. Your information is encrypted in transit and at rest.")
                        .font(.body)

                    Text("Contact Us")
                        .font(.headline)

                    Text("If you have any questions about this privacy policy, please contact <NAME_EMAIL>")
                        .font(.body)
                }
                .padding(20)
            }
            .navigationTitle("Privacy Policy")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
