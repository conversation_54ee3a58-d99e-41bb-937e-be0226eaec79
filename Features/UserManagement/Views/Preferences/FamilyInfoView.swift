import SwiftUI

// Type alias to avoid naming conflicts with AppCoordinator's legacy view
typealias UserManagement_FamilyInfoView = FamilyInfoView

/// Complete family information management interface
/// 
/// Allows users to manage their household information including
/// family size, children details, and special dietary needs.
/// 
/// Features:
/// - Family member count management
/// - Children information (ages and special needs)
/// - Special dietary requirements per family member
/// - Recipe portion scaling based on family size
/// - Accessibility support
struct FamilyInfoView: View {
    
    // MARK: - Environment Objects
    
    @EnvironmentObject private var profileService: UserProfileService
    @EnvironmentObject private var appPreferences: AppPreferences
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var memberCount: Int = 1
    @State private var hasChildren: Bool = false
    @State private var childrenAges: [Int] = []
    @State private var specialDietaryNeeds: [String] = []
    @State private var notes: String = ""
    
    @State private var newChildAge: String = ""
    @State private var newSpecialNeed: String = ""
    @State private var showingAddChild = false
    @State private var showingAddSpecialNeed = false
    @State private var isLoading = false
    @State private var errorMessage: String?
    
    // MARK: - Computed Properties
    
    private var fontScale: CGFloat {
        appPreferences?.fontSize.scaleFactor ?? 1.0
    }
    
    private var hasChanges: Bool {
        guard let profile = profileService.userProfile else { return false }
        let familyInfo = profile.familyInfo
        return memberCount != familyInfo.memberCount ||
               hasChildren != familyInfo.hasChildren ||
               childrenAges != familyInfo.childrenAges ||
               specialDietaryNeeds != familyInfo.specialDietaryNeeds ||
               notes != familyInfo.notes
    }
    
    private var servingSizeMultiplier: Double {
        max(1.0, Double(memberCount) * 0.8) // Slightly less than linear scaling
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24 * fontScale) {
                    familySizeSection
                    childrenSection
                    specialNeedsSection
                    notesSection
                    servingInfoSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Family Information")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .scaleEffect(fontScale)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(!hasChanges || isLoading)
                    .scaleEffect(fontScale)
                }
            }
            .onAppear {
                loadCurrentInfo()
            }
            .alert("Error", isPresented: .constant(errorMessage != nil)) {
                Button("OK") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
        }
    }
    
    // MARK: - Family Size Section
    
    private var familySizeSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Family Size")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("How many people are in your household?")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
            }
            
            // Member Count Picker
            VStack(spacing: 12) {
                HStack {
                    Text("Number of Members")
                        .font(.body.weight(.medium))
                        .scaleEffect(fontScale)
                    
                    Spacer()
                    
                    HStack(spacing: 16) {
                        Button(action: {
                            if memberCount > 1 {
                                memberCount -= 1
                                updateChildrenForMemberCount()
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.title2)
                                .foregroundColor(memberCount > 1 ? .blue : .gray)
                        }
                        .disabled(memberCount <= 1)
                        .scaleEffect(fontScale)
                        
                        Text("\(memberCount)")
                            .font(.title2.weight(.bold))
                            .frame(minWidth: 40)
                            .scaleEffect(fontScale)
                        
                        Button(action: {
                            if memberCount < 20 {
                                memberCount += 1
                            }
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.title2)
                                .foregroundColor(memberCount < 20 ? .blue : .gray)
                        }
                        .disabled(memberCount >= 20)
                        .scaleEffect(fontScale)
                    }
                }
                
                // Visual representation
                HStack(spacing: 4) {
                    ForEach(0..<min(memberCount, 10), id: \.self) { _ in
                        Image(systemName: "person.fill")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }
                    
                    if memberCount > 10 {
                        Text("+\(memberCount - 10)")
                            .font(.caption.weight(.medium))
                            .foregroundColor(.blue)
                            .scaleEffect(fontScale)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
    
    // MARK: - Children Section
    
    private var childrenSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Children Information")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Ages help us suggest age-appropriate recipes")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                if hasChildren {
                    Button(action: {
                        showingAddChild = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.purple)
                    }
                    .scaleEffect(fontScale)
                    .accessibilityLabel("Add child")
                }
            }
            
            // Has Children Toggle
            HStack {
                Text("Do you have children?")
                    .font(.body.weight(.medium))
                    .scaleEffect(fontScale)
                
                Spacer()
                
                Toggle("", isOn: $hasChildren)
                    .scaleEffect(fontScale)
                    .onChange(of: hasChildren) { newValue in
                        if !newValue {
                            childrenAges.removeAll()
                        }
                    }
            }
            
            // Children Ages List
            if hasChildren {
                if childrenAges.isEmpty {
                    VStack(spacing: 8) {
                        Image(systemName: "figure.and.child.holdinghands")
                            .font(.title)
                            .foregroundColor(.secondary)
                        
                        Text("No children added yet")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .scaleEffect(fontScale)
                        
                        Button("Add Child") {
                            showingAddChild = true
                        }
                        .buttonStyle(.bordered)
                        .scaleEffect(fontScale)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 24)
                    .background(Color(.tertiarySystemGroupedBackground))
                    .cornerRadius(12)
                } else {
                    LazyVGrid(columns: [
                        GridItem(.adaptive(minimum: 80), spacing: 8)
                    ], spacing: 8) {
                        ForEach(Array(childrenAges.enumerated()), id: \.offset) { index, age in
                            ChildAgeTag(
                                age: age,
                                fontScale: fontScale
                            ) {
                                childrenAges.remove(at: index)
                            }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
        .sheet(isPresented: $showingAddChild) {
            AddChildSheet(
                childAge: $newChildAge
            ) {
                if let age = Int(newChildAge), age > 0 && age <= 18 {
                    childrenAges.append(age)
                    childrenAges.sort()
                    newChildAge = ""
                }
            }
        }
    }
    
    // MARK: - Special Needs Section
    
    private var specialNeedsSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Special Dietary Needs")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)
                    
                    Text("Family members with specific dietary requirements")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                
                Spacer()
                
                Button(action: {
                    showingAddSpecialNeed = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(.pink)
                }
                .scaleEffect(fontScale)
                .accessibilityLabel("Add special dietary need")
            }
            
            // Special Needs List
            if specialDietaryNeeds.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "heart.text.square")
                        .font(.title)
                        .foregroundColor(.secondary)
                    
                    Text("No special dietary needs")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 32)
                .background(Color(.tertiarySystemGroupedBackground))
                .cornerRadius(12)
            } else {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 120), spacing: 8)
                ], spacing: 8) {
                    ForEach(Array(specialDietaryNeeds.enumerated()), id: \.offset) { index, need in
                        SpecialNeedTag(
                            text: need,
                            fontScale: fontScale
                        ) {
                            specialDietaryNeeds.remove(at: index)
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
        .sheet(isPresented: $showingAddSpecialNeed) {
            AddSpecialNeedSheet(
                specialNeed: $newSpecialNeed
            ) {
                if !newSpecialNeed.isEmpty && !specialDietaryNeeds.contains(newSpecialNeed) {
                    specialDietaryNeeds.append(newSpecialNeed)
                    newSpecialNeed = ""
                }
            }
        }
    }

    // MARK: - Notes Section

    private var notesSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Additional Notes")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)

                    Text("Any other family information that might help with meal planning")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()
            }

            // Notes Text Field
            TextField("Enter any additional family information...", text: $notes, axis: .vertical)
                .textFieldStyle(.roundedBorder)
                .lineLimit(3...6)
                .scaleEffect(fontScale)
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }

    // MARK: - Serving Info Section

    private var servingInfoSection: some View {
        VStack(spacing: 16 * fontScale) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Recipe Serving Information")
                        .font(.title2.weight(.semibold))
                        .scaleEffect(fontScale)

                    Text("How this affects recipe portions")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()
            }

            // Serving Multiplier Info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Serving Size Multiplier")
                        .font(.body.weight(.medium))
                        .scaleEffect(fontScale)

                    Text("Recipes will be scaled by \(servingSizeMultiplier, specifier: "%.1f")x")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)
                }

                Spacer()

                Text("\(servingSizeMultiplier, specifier: "%.1f")x")
                    .font(.title2.weight(.bold))
                    .foregroundColor(.blue)
                    .scaleEffect(fontScale)
            }

            // Example
            VStack(alignment: .leading, spacing: 8) {
                Text("Example:")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.secondary)
                    .scaleEffect(fontScale)

                HStack {
                    Text("Recipe for 4 people →")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .scaleEffect(fontScale)

                    Text("Adjusted for \(Int(4 * servingSizeMultiplier)) people")
                        .font(.caption.weight(.medium))
                        .foregroundColor(.blue)
                        .scaleEffect(fontScale)
                }
            }
            .padding(.top, 8)
        }
        .padding(16)
        .background(Color(.secondarySystemGroupedBackground))
        .cornerRadius(12)
    }
}

// MARK: - Private Implementation

private extension FamilyInfoView {

    func loadCurrentInfo() {
        guard let profile = profileService.userProfile else { return }
        let familyInfo = profile.familyInfo

        memberCount = familyInfo.memberCount
        hasChildren = familyInfo.hasChildren
        childrenAges = familyInfo.childrenAges
        specialDietaryNeeds = familyInfo.specialDietaryNeeds
        notes = familyInfo.notes
    }

    func updateChildrenForMemberCount() {
        // If member count is less than current children + adults, remove some children
        let maxChildren = max(0, memberCount - 1) // Assume at least 1 adult
        if childrenAges.count > maxChildren {
            childrenAges = Array(childrenAges.prefix(maxChildren))
        }

        // If no members left for children, disable children
        if memberCount <= 1 {
            hasChildren = false
            childrenAges.removeAll()
        }
    }

    func saveChanges() async {
        isLoading = true
        errorMessage = nil

        do {
            guard let userId = profileService.userProfile?.id else {
                throw NSError(domain: "FamilyInfo", code: 1, userInfo: [NSLocalizedDescriptionKey: "User not found"])
            }

            let familyInfo = FamilyInfo(
                memberCount: memberCount,
                hasChildren: hasChildren,
                childrenAges: childrenAges,
                specialDietaryNeeds: specialDietaryNeeds,
                notes: notes
            )

            try await profileService.updateFamilyInfo(userId: userId, familyInfo: familyInfo)

            // Save to UserDefaults as backup
            await MainActor.run {
                UserDefaults.standard.set(familyInfo.memberCount, forKey: "familyMemberCount")
                UserDefaults.standard.set(familyInfo.hasChildren, forKey: "familyHasChildren")
                UserDefaults.standard.set(familyInfo.childrenAges, forKey: "familyChildrenAges")

                // Send notification for UI updates
                NotificationCenter.default.post(name: NSNotification.Name("FamilyInfoUpdated"), object: nil)

                // Haptic feedback
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)

                // Dismiss on success
                dismiss()
            }

        } catch {
            await MainActor.run {
                errorMessage = "Failed to save family information: \(error.localizedDescription)"
            }
        }

        isLoading = false
    }
}

// MARK: - Supporting Components

/// Tag component for child ages
struct ChildAgeTag: View {
    let age: Int
    let fontScale: CGFloat
    let onRemove: () -> Void

    var body: some View {
        HStack(spacing: 6) {
            Text("\(age) yr")
                .font(.caption.weight(.medium))
                .scaleEffect(fontScale)

            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.white)
            }
            .accessibilityLabel("Remove child age \(age)")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.purple)
        .foregroundColor(.white)
        .cornerRadius(16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Child age \(age) years. Tap to remove")
    }
}

/// Tag component for special dietary needs
struct SpecialNeedTag: View {
    let text: String
    let fontScale: CGFloat
    let onRemove: () -> Void

    var body: some View {
        HStack(spacing: 6) {
            Text(text.capitalized)
                .font(.caption.weight(.medium))
                .scaleEffect(fontScale)

            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.white)
            }
            .accessibilityLabel("Remove \(text)")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.pink)
        .foregroundColor(.white)
        .cornerRadius(16)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(text). Tap to remove")
    }
}

/// Sheet for adding child age
struct AddChildSheet: View {
    @Binding var childAge: String
    let onAdd: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Child's Age")
                        .font(.headline)

                    TextField("Enter age (1-18)", text: $childAge)
                        .textFieldStyle(.roundedBorder)
                        .keyboardType(.numberPad)
                }

                Spacer()
            }
            .padding(20)
            .navigationTitle("Add Child")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        onAdd()
                        dismiss()
                    }
                    .disabled(childAge.isEmpty || Int(childAge) == nil || Int(childAge)! < 1 || Int(childAge)! > 18)
                }
            }
        }
    }
}

/// Sheet for adding special dietary need
struct AddSpecialNeedSheet: View {
    @Binding var specialNeed: String
    let onAdd: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Special Dietary Need")
                        .font(.headline)

                    TextField("e.g., Diabetes, Low sodium for grandpa", text: $specialNeed)
                        .textFieldStyle(.roundedBorder)
                        .autocapitalization(.sentences)
                }

                Spacer()
            }
            .padding(20)
            .navigationTitle("Add Special Need")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        onAdd()
                        dismiss()
                    }
                    .disabled(specialNeed.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}
