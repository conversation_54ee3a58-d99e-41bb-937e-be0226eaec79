# Preferences Views Directory

This directory contains user preference-related views:

- FoodPreferencesView.swift - Food restrictions and preferences
- FamilyInfoView.swift - Family information settings
- NotificationSettingsView.swift - Notification preferences
- AppPreferencesView.swift - Application settings

Views should:
- Provide clear preference options
- Use appropriate input controls
- Save changes automatically or with clear save actions
- Provide helpful descriptions for settings
