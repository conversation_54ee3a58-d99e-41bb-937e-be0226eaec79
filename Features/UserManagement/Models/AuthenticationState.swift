import Foundation

/// Represents the current authentication state of the application
/// Thread-safe with Swift 6.0 Sendable conformance
enum AuthenticationState: Sendable, Equatable {
    case loading
    case unauthenticated
    case authenticated(User)
    case error(AuthenticationError)
}

/// Authentication-related errors
enum AuthenticationError: LocalizedError, Sendable, Equatable {
    case networkError
    case invalidCredentials
    case userNotFound
    case emailAlreadyInUse
    case weakPassword
    case userDisabled
    case tooManyRequests
    case operationNotAllowed
    case invalidEmail
    case requiresRecentLogin
    case providerAlreadyLinked
    case noSuchProvider
    case invalidUserToken
    case networkRequestFailed
    case userTokenExpired
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .networkError:
            return "Network connection error. Please check your internet connection."
        case .invalidCredentials:
            return "Invalid email or password. Please try again."
        case .userNotFound:
            return "No account found with this email address."
        case .emailAlreadyInUse:
            return "An account already exists with this email address."
        case .weakPassword:
            return "Password is too weak. Please choose a stronger password."
        case .userDisabled:
            return "This account has been disabled. Please contact support."
        case .tooManyRequests:
            return "Too many failed attempts. Please try again later."
        case .operationNotAllowed:
            return "This sign-in method is not enabled. Please contact support."
        case .invalidEmail:
            return "Please enter a valid email address."
        case .requiresRecentLogin:
            return "This operation requires recent authentication. Please sign in again."
        case .providerAlreadyLinked:
            return "This account is already linked to another provider."
        case .noSuchProvider:
            return "This account is not linked to the specified provider."
        case .invalidUserToken:
            return "Your session has expired. Please sign in again."
        case .networkRequestFailed:
            return "Network request failed. Please try again."
        case .userTokenExpired:
            return "Your session has expired. Please sign in again."
        case .unknown(let message):
            return "An unexpected error occurred: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkError, .networkRequestFailed:
            return "Check your internet connection and try again."
        case .invalidCredentials, .userNotFound:
            return "Verify your email and password, or create a new account."
        case .emailAlreadyInUse:
            return "Try signing in instead, or use a different email address."
        case .weakPassword:
            return "Use at least 8 characters with a mix of letters, numbers, and symbols."
        case .tooManyRequests:
            return "Wait a few minutes before trying again."
        case .requiresRecentLogin, .invalidUserToken, .userTokenExpired:
            return "Sign out and sign in again to continue."
        default:
            return "If the problem persists, please contact support."
        }
    }
}

// MARK: - AuthenticationState Extensions

extension AuthenticationState {
    /// Returns true if the user is authenticated
    var isAuthenticated: Bool {
        if case .authenticated = self {
            return true
        }
        return false
    }
    
    /// Returns the authenticated user, if any
    var user: User? {
        if case .authenticated(let user) = self {
            return user
        }
        return nil
    }
    
    /// Returns the current error, if any
    var error: AuthenticationError? {
        if case .error(let error) = self {
            return error
        }
        return nil
    }
    
    /// Returns true if the state is loading
    var isLoading: Bool {
        if case .loading = self {
            return true
        }
        return false
    }
}

/// Sign-in method types
enum SignInMethod: String, CaseIterable, Sendable {
    case apple = "apple"
    case google = "google"
    case email = "email"
    
    var displayName: String {
        switch self {
        case .apple:
            return "Continue with Apple"
        case .google:
            return "Continue with Google"
        case .email:
            return "Continue with Email"
        }
    }
    
    var iconName: String {
        switch self {
        case .apple:
            return "applelogo"
        case .google:
            return "globe"
        case .email:
            return "envelope"
        }
    }
    
    var backgroundColor: String {
        switch self {
        case .apple:
            return "black"
        case .google:
            return "blue"
        case .email:
            return "gray"
        }
    }
}
