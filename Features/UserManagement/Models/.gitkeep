# Models Directory

This directory contains all data models for the User Management module:

- UserProfile.swift - Main user profile data model
- DietaryRestriction.swift - Dietary restrictions enumeration
- FamilyInfo.swift - Family information structure
- NotificationSettings.swift - Notification preferences
- AppPreferences.swift - Application preferences

All models should:
- Conform to Codable for Firebase integration
- Conform to Identifiable where appropriate
- Use proper access control
- Include comprehensive documentation
