import Foundation

/// User notification preferences for the application
/// 
/// Controls when and how the user receives notifications about
/// ingredient expiry, shopping reminders, and recipe suggestions.
/// 
/// - Note: All settings default to user-friendly values
struct NotificationSettings: Codable, Sendable, Equatable {
    // Ingredient Management Notifications
    var ingredientExpiryReminders: Bool
    var expiryReminderDays: Int // Days before expiry to remind
    var lowStockAlerts: Bool
    
    // Shopping List Notifications
    var shoppingListReminders: Bool
    var shoppingReminderTime: NotificationTime?
    var shoppingReminderDays: [WeekDay] // Which days to remind
    
    // Recipe and Meal Planning
    var recipeRecommendations: Bool
    var mealPlanningReminders: Bool
    var mealPlanningTime: NotificationTime?
    
    // General App Notifications
    var appUpdates: Bool
    var tips: Bool
    var marketingEmails: Bool
    
    /// Initialize with user-friendly defaults
    init(
        ingredientExpiryReminders: Bool = true,
        expiryReminderDays: Int = 2,
        lowStockAlerts: Bool = true,
        shoppingListReminders: Bool = true,
        shoppingReminderTime: NotificationTime? = NotificationTime(hour: 10, minute: 0),
        shoppingReminderDays: [WeekDay] = [.saturday],
        recipeRecommendations: Bool = true,
        mealPlanningReminders: Bool = false,
        mealPlanningTime: NotificationTime? = NotificationTime(hour: 18, minute: 0),
        appUpdates: Bool = true,
        tips: Bool = true,
        marketingEmails: Bool = false
    ) {
        self.ingredientExpiryReminders = ingredientExpiryReminders
        self.expiryReminderDays = max(1, min(7, expiryReminderDays)) // 1-7 days
        self.lowStockAlerts = lowStockAlerts
        self.shoppingListReminders = shoppingListReminders
        self.shoppingReminderTime = shoppingReminderTime
        self.shoppingReminderDays = shoppingReminderDays
        self.recipeRecommendations = recipeRecommendations
        self.mealPlanningReminders = mealPlanningReminders
        self.mealPlanningTime = mealPlanningTime
        self.appUpdates = appUpdates
        self.tips = tips
        self.marketingEmails = marketingEmails
    }
}

// MARK: - NotificationSettings Extensions

extension NotificationSettings {
    /// Returns true if any notifications are enabled
    var hasAnyNotificationsEnabled: Bool {
        ingredientExpiryReminders || lowStockAlerts || shoppingListReminders ||
        recipeRecommendations || mealPlanningReminders || appUpdates || tips
    }
    
    /// Returns true if critical notifications are enabled
    var hasCriticalNotificationsEnabled: Bool {
        ingredientExpiryReminders || lowStockAlerts
    }
    
    /// Returns count of enabled notification types
    var enabledNotificationCount: Int {
        var count = 0
        if ingredientExpiryReminders { count += 1 }
        if lowStockAlerts { count += 1 }
        if shoppingListReminders { count += 1 }
        if recipeRecommendations { count += 1 }
        if mealPlanningReminders { count += 1 }
        if appUpdates { count += 1 }
        if tips { count += 1 }
        return count
    }
}

/// Time for scheduled notifications
struct NotificationTime: Codable, Sendable, Equatable {
    let hour: Int // 0-23
    let minute: Int // 0-59
    
    init(hour: Int, minute: Int) {
        self.hour = max(0, min(23, hour))
        self.minute = max(0, min(59, minute))
    }
    
    /// Returns formatted time string (12-hour format)
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        
        let calendar = Calendar.current
        let date = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: Date()) ?? Date()
        return formatter.string(from: date)
    }
    
    /// Returns formatted time string (24-hour format)
    var formattedTime24Hour: String {
        return String(format: "%02d:%02d", hour, minute)
    }
}

/// Days of the week for recurring notifications
enum WeekDay: String, CaseIterable, Codable, Sendable {
    case sunday = "Sunday"
    case monday = "Monday"
    case tuesday = "Tuesday"
    case wednesday = "Wednesday"
    case thursday = "Thursday"
    case friday = "Friday"
    case saturday = "Saturday"
    
    var shortName: String {
        switch self {
        case .sunday: return "Sun"
        case .monday: return "Mon"
        case .tuesday: return "Tue"
        case .wednesday: return "Wed"
        case .thursday: return "Thu"
        case .friday: return "Fri"
        case .saturday: return "Sat"
        }
    }
    
    var weekdayNumber: Int {
        switch self {
        case .sunday: return 1
        case .monday: return 2
        case .tuesday: return 3
        case .wednesday: return 4
        case .thursday: return 5
        case .friday: return 6
        case .saturday: return 7
        }
    }
}

/// Notification categories for grouping settings
enum NotificationCategory: String, CaseIterable, Sendable {
    case ingredients = "Ingredients"
    case shopping = "Shopping"
    case recipes = "Recipes"
    case general = "General"
    
    var description: String {
        switch self {
        case .ingredients:
            return "Ingredient expiry and stock alerts"
        case .shopping:
            return "Shopping list and reminder notifications"
        case .recipes:
            return "Recipe recommendations and meal planning"
        case .general:
            return "App updates and tips"
        }
    }
    
    var iconName: String {
        switch self {
        case .ingredients:
            return "leaf.fill"
        case .shopping:
            return "cart.fill"
        case .recipes:
            return "flame.fill"
        case .general:
            return "app.badge"
        }
    }
}
