import Foundation
import FirebaseAuth

/// Basic user model representing an authenticated user
///
/// This model represents a user in the authentication system and follows
/// Swift 6.0 concurrency guidelines with Sendable conformance for thread safety.
///
/// - Note: All properties are immutable to ensure thread safety
/// - Important: The `id` field corresponds to Firebase Auth UID
struct User: Identifiable, Codable, Sendable, Equatable {
    let id: String
    let email: String?
    let displayName: String?
    let photoURL: String?
    let isEmailVerified: Bool
    let createdAt: Date
    let lastSignInAt: Date?
    
    /// Initialize from Firebase User
    init(from firebaseUser: FirebaseAuth.User) {
        self.id = firebaseUser.uid
        self.email = firebaseUser.email
        self.displayName = firebaseUser.displayName
        self.photoURL = firebaseUser.photoURL?.absoluteString
        self.isEmailVerified = firebaseUser.isEmailVerified
        self.createdAt = firebaseUser.metadata.creationDate ?? Date()
        self.lastSignInAt = firebaseUser.metadata.lastSignInDate
    }
    
    /// Initialize with custom data (for testing or manual creation)
    init(
        id: String,
        email: String? = nil,
        displayName: String? = nil,
        photoURL: String? = nil,
        isEmailVerified: Bool = false,
        createdAt: Date = Date(),
        lastSignInAt: Date? = nil
    ) {
        self.id = id
        self.email = email
        self.displayName = displayName
        self.photoURL = photoURL
        self.isEmailVerified = isEmailVerified
        self.createdAt = createdAt
        self.lastSignInAt = lastSignInAt
    }
}

// MARK: - User Extensions

extension User {
    /// Returns the best available display name
    var bestDisplayName: String {
        if let displayName = displayName, !displayName.isEmpty {
            return displayName
        }
        if let email = email {
            return email.components(separatedBy: "@").first ?? email
        }
        return "User"
    }
    
    /// Returns true if user has a profile photo
    var hasProfilePhoto: Bool {
        photoURL != nil && !photoURL!.isEmpty
    }
    
    /// Returns the authentication provider type
    var primaryProvider: AuthenticationProvider {
        guard let email = email else { return .anonymous }
        
        if email.contains("@privaterelay.appleid.com") {
            return .apple
        } else if email.contains("@gmail.com") || email.contains("@googlemail.com") {
            return .google
        } else {
            return .email
        }
    }
}

/// Authentication provider types
enum AuthenticationProvider: String, CaseIterable, Codable, Sendable {
    case apple = "apple.com"
    case google = "google.com"
    case email = "password"
    case anonymous = "anonymous"
    
    var displayName: String {
        switch self {
        case .apple:
            return "Apple"
        case .google:
            return "Google"
        case .email:
            return "Email"
        case .anonymous:
            return "Anonymous"
        }
    }
    
    var iconName: String {
        switch self {
        case .apple:
            return "applelogo"
        case .google:
            return "globe"
        case .email:
            return "envelope"
        case .anonymous:
            return "person.circle"
        }
    }
}
