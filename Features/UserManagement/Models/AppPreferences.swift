import Foundation

/// Application-specific user preferences
/// 
/// Contains settings for app behavior, appearance, and functionality
/// that affect the overall user experience.
/// 
/// - Note: All preferences have sensible defaults for new users
struct AppPreferences: Codable, Sendable, Equatable {
    // Display Settings
    var fontSize: FontSize
    var language: AppLanguage
    var measurementUnit: MeasurementUnit
    
    // App Behavior
    var autoSavePreferences: Bool
    var showTips: Bool
    var enableHapticFeedback: Bool
    var enableSounds: Bool
    
    // Privacy Settings
    var shareUsageData: Bool
    var shareRecipeData: Bool
    var allowPersonalization: Bool
    
    // Advanced Settings
    var debugMode: Bool
    var betaFeatures: Bool
    
    /// Initialize with user-friendly defaults
    init(
        fontSize: FontSize = .medium,
        language: AppLanguage = .english,
        measurementUnit: MeasurementUnit = .metric,
        autoSavePreferences: Bool = true,
        showTips: Bool = true,
        enableHapticFeedback: Bool = true,
        enableSounds: Bool = true,
        shareUsageData: Bool = false,
        shareRecipeData: Bool = false,
        allowPersonalization: Bool = true,
        debugMode: Bool = false,
        betaFeatures: Bool = false
    ) {
        self.fontSize = fontSize
        self.language = language
        self.measurementUnit = measurementUnit
        self.autoSavePreferences = autoSavePreferences
        self.showTips = showTips
        self.enableHapticFeedback = enableHapticFeedback
        self.enableSounds = enableSounds
        self.shareUsageData = shareUsageData
        self.shareRecipeData = shareRecipeData
        self.allowPersonalization = allowPersonalization
        self.debugMode = debugMode
        self.betaFeatures = betaFeatures
    }
}

// MARK: - Font Size Options

enum FontSize: String, CaseIterable, Codable, Sendable {
    case small = "Small"
    case medium = "Medium"
    case large = "Large"
    case extraLarge = "Extra Large"
    
    var description: String {
        switch self {
        case .small:
            return "Small text for more content"
        case .medium:
            return "Standard text size"
        case .large:
            return "Large text for better readability"
        case .extraLarge:
            return "Extra large text for accessibility"
        }
    }
    
    var scaleFactor: CGFloat {
        switch self {
        case .small:
            return 0.9
        case .medium:
            return 1.0
        case .large:
            return 1.2
        case .extraLarge:
            return 1.4
        }
    }
}

// MARK: - Language Options

enum AppLanguage: String, CaseIterable, Codable, Sendable {
    case english = "en"
    case spanish = "es"
    case french = "fr"
    case german = "de"
    case italian = "it"
    case portuguese = "pt"
    case chinese = "zh"
    case japanese = "ja"
    case korean = "ko"
    
    var displayName: String {
        switch self {
        case .english:
            return "English"
        case .spanish:
            return "Español"
        case .french:
            return "Français"
        case .german:
            return "Deutsch"
        case .italian:
            return "Italiano"
        case .portuguese:
            return "Português"
        case .chinese:
            return "中文"
        case .japanese:
            return "日本語"
        case .korean:
            return "한국어"
        }
    }
    
    var nativeName: String {
        return displayName
    }
}

// MARK: - Measurement Units

enum MeasurementUnit: String, CaseIterable, Codable, Sendable {
    case metric = "Metric"
    case imperial = "Imperial"
    case mixed = "Mixed"
    
    var description: String {
        switch self {
        case .metric:
            return "Metric (kg, g, L, mL, °C)"
        case .imperial:
            return "Imperial (lb, oz, gal, fl oz, °F)"
        case .mixed:
            return "Mixed (best of both systems)"
        }
    }
    
    var temperatureUnit: String {
        switch self {
        case .metric, .mixed:
            return "°C"
        case .imperial:
            return "°F"
        }
    }
    
    var weightUnit: String {
        switch self {
        case .metric, .mixed:
            return "g"
        case .imperial:
            return "oz"
        }
    }
    
    var volumeUnit: String {
        switch self {
        case .metric:
            return "mL"
        case .imperial:
            return "fl oz"
        case .mixed:
            return "cup"
        }
    }
}

// MARK: - AppPreferences Extensions

extension AppPreferences {
    /// Returns true if accessibility features are enabled
    var hasAccessibilityFeaturesEnabled: Bool {
        fontSize == .large || fontSize == .extraLarge
    }
    
    /// Returns true if privacy-conscious settings are enabled
    var hasPrivacyFeaturesEnabled: Bool {
        !shareUsageData && !shareRecipeData
    }
    
    /// Returns true if enhanced features are enabled
    var hasEnhancedFeaturesEnabled: Bool {
        enableHapticFeedback && enableSounds && allowPersonalization
    }
    
    /// Returns localized measurement format for recipes
    func formatMeasurement(_ amount: Double, unit: String) -> String {
        let formatter = NumberFormatter()
        formatter.maximumFractionDigits = 2
        formatter.minimumFractionDigits = 0
        
        let formattedAmount = formatter.string(from: NSNumber(value: amount)) ?? "\(amount)"
        
        switch measurementUnit {
        case .metric:
            return "\(formattedAmount) \(unit)"
        case .imperial:
            // Convert common metric units to imperial
            let convertedUnit = convertToImperial(unit: unit)
            let convertedAmount = convertAmountToImperial(amount: amount, unit: unit)
            let convertedAmountStr = formatter.string(from: NSNumber(value: convertedAmount)) ?? "\(convertedAmount)"
            return "\(convertedAmountStr) \(convertedUnit)"
        case .mixed:
            // Use the most intuitive unit for each measurement
            return "\(formattedAmount) \(unit)"
        }
    }
    
    private func convertToImperial(unit: String) -> String {
        switch unit.lowercased() {
        case "g", "grams":
            return "oz"
        case "kg", "kilograms":
            return "lb"
        case "ml", "milliliters":
            return "fl oz"
        case "l", "liters":
            return "qt"
        default:
            return unit
        }
    }
    
    private func convertAmountToImperial(amount: Double, unit: String) -> Double {
        switch unit.lowercased() {
        case "g", "grams":
            return amount * 0.035274 // grams to ounces
        case "kg", "kilograms":
            return amount * 2.20462 // kg to pounds
        case "ml", "milliliters":
            return amount * 0.033814 // ml to fl oz
        case "l", "liters":
            return amount * 1.05669 // liters to quarts
        default:
            return amount
        }
    }
}
