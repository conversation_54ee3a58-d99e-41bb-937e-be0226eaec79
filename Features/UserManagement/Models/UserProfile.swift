import Foundation

/// Comprehensive user profile containing preferences and personal information
/// 
/// This model stores all user-specific data including food preferences,
/// family information, and app settings. Designed for Firebase Firestore
/// storage with proper Codable conformance.
/// 
/// - Note: All nested types are also Sendable for thread safety
/// - Important: Changes to this model may require database migration
struct UserProfile: Identifiable, Codable, Sendable, Equatable {
    let id: String // Matches Firebase Auth UID
    var email: String?
    var displayName: String?
    var photoURL: String?
    
    // Food Preferences
    var strictExclusions: [String] // Foods that should never appear
    var dietaryRestrictions: [DietaryRestriction]
    var allergies: [String]
    
    // Family Information
    var familyInfo: FamilyInfo
    
    // App Preferences
    var notificationSettings: NotificationSettings
    var appPreferences: AppPreferences
    
    // Metadata
    var createdAt: Date
    var updatedAt: Date
    var version: Int // For data migration purposes
    
    /// Initialize with default values
    init(
        id: String,
        email: String? = nil,
        displayName: String? = nil,
        photoURL: String? = nil
    ) {
        self.id = id
        self.email = email
        self.displayName = displayName
        self.photoURL = photoURL
        
        // Initialize with empty/default preferences
        self.strictExclusions = []
        self.dietaryRestrictions = []
        self.allergies = []
        self.familyInfo = FamilyInfo()
        self.notificationSettings = NotificationSettings()
        self.appPreferences = AppPreferences()
        
        // Set metadata
        let now = Date()
        self.createdAt = now
        self.updatedAt = now
        self.version = 1
    }
    
    /// Update the profile and increment version
    mutating func markAsUpdated() {
        self.updatedAt = Date()
        self.version += 1
    }
}

// MARK: - UserProfile Extensions

extension UserProfile {
    /// Returns the best available display name
    var bestDisplayName: String {
        if let displayName = displayName, !displayName.isEmpty {
            return displayName
        }
        if let email = email {
            return email.components(separatedBy: "@").first ?? email
        }
        return "User"
    }
    
    /// Returns true if user has any dietary restrictions
    var hasDietaryRestrictions: Bool {
        !dietaryRestrictions.isEmpty || !allergies.isEmpty || !strictExclusions.isEmpty
    }
    
    /// Returns all restricted ingredients as a combined list
    var allRestrictedIngredients: [String] {
        var restricted = strictExclusions
        restricted.append(contentsOf: allergies)
        return Array(Set(restricted)) // Remove duplicates
    }
    
    /// Returns true if the ingredient should be excluded
    ///
    /// This method checks against strict exclusions, allergies, and dietary restrictions
    /// in order of priority. Uses optimized string matching for better performance.
    ///
    /// - Parameter ingredient: The ingredient name to check
    /// - Returns: True if the ingredient should be excluded from recipes
    func shouldExcludeIngredient(_ ingredient: String) -> Bool {
        let lowercased = ingredient.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)

        // Early return for empty ingredients
        guard !lowercased.isEmpty else { return false }

        // Check strict exclusions (highest priority)
        if strictExclusions.contains(where: { $0.lowercased() == lowercased }) {
            return true
        }

        // Check allergies (second priority)
        if allergies.contains(where: { $0.lowercased() == lowercased }) {
            return true
        }

        // Check dietary restrictions (third priority)
        // Use contains(where:) for better performance than manual loop
        return dietaryRestrictions.contains { restriction in
            restriction.excludesIngredient(ingredient)
        }
    }
}

/// Dietary restriction types with ingredient exclusion logic
enum DietaryRestriction: String, CaseIterable, Codable, Sendable {
    case vegetarian = "Vegetarian"
    case vegan = "Vegan"
    case glutenFree = "Gluten-Free"
    case dairyFree = "Dairy-Free"
    case nutFree = "Nut-Free"
    case lowCarb = "Low-Carb"
    case keto = "Keto"
    case paleo = "Paleo"
    case halal = "Halal"
    case kosher = "Kosher"
    
    var description: String {
        switch self {
        case .vegetarian:
            return "No meat, poultry, or fish"
        case .vegan:
            return "No animal products"
        case .glutenFree:
            return "No gluten-containing grains"
        case .dairyFree:
            return "No dairy products"
        case .nutFree:
            return "No nuts or nut products"
        case .lowCarb:
            return "Limited carbohydrates"
        case .keto:
            return "Very low carb, high fat"
        case .paleo:
            return "Whole foods, no processed items"
        case .halal:
            return "Islamic dietary laws"
        case .kosher:
            return "Jewish dietary laws"
        }
    }
    
    /// Check if this restriction excludes a specific ingredient
    func excludesIngredient(_ ingredient: String) -> Bool {
        let lowercased = ingredient.lowercased()
        
        switch self {
        case .vegetarian:
            return ["meat", "beef", "pork", "chicken", "fish", "seafood"].contains { lowercased.contains($0) }
        case .vegan:
            return ["meat", "beef", "pork", "chicken", "fish", "seafood", "dairy", "milk", "cheese", "egg", "honey"].contains { lowercased.contains($0) }
        case .glutenFree:
            return ["wheat", "barley", "rye", "gluten"].contains { lowercased.contains($0) }
        case .dairyFree:
            return ["milk", "cheese", "butter", "cream", "dairy"].contains { lowercased.contains($0) }
        case .nutFree:
            return ["nut", "almond", "walnut", "peanut", "cashew", "pecan"].contains { lowercased.contains($0) }
        case .lowCarb, .keto:
            return ["bread", "pasta", "rice", "potato", "sugar"].contains { lowercased.contains($0) }
        case .paleo:
            return ["grain", "dairy", "legume", "processed"].contains { lowercased.contains($0) }
        case .halal:
            return ["pork", "alcohol"].contains { lowercased.contains($0) }
        case .kosher:
            return ["pork", "shellfish", "mixing meat and dairy"].contains { lowercased.contains($0) }
        }
    }
}
