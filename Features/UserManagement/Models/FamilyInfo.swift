import Foundation

/// Family information for household cooking planning
/// 
/// Contains information about family size and special dietary needs
/// to help with recipe generation and portion planning.
/// 
/// - Note: All properties are designed for easy recipe scaling
struct FamilyInfo: Codable, Sendable, Equatable {
    var memberCount: Int
    var hasChildren: Bool
    var childrenAges: [Int] // Ages of children for age-appropriate recipes
    var specialDietaryNeeds: [String] // Family members with special needs
    var notes: String // Additional family information
    
    /// Initialize with default single-person household
    init(
        memberCount: Int = 1,
        hasChildren: Bool = false,
        childrenAges: [Int] = [],
        specialDietaryNeeds: [String] = [],
        notes: String = ""
    ) {
        self.memberCount = max(1, memberCount) // Ensure at least 1 member
        self.hasChildren = hasChildren
        self.childrenAges = childrenAges
        self.specialDietaryNeeds = specialDietaryNeeds
        self.notes = notes
    }
}

// MARK: - FamilyInfo Extensions

extension FamilyInfo {
    /// Returns true if family has any special considerations
    var hasSpecialConsiderations: Bool {
        hasChildren || !specialDietaryNeeds.isEmpty || !notes.isEmpty
    }
    
    /// Returns the youngest child's age, if any
    var youngestChildAge: Int? {
        childrenAges.min()
    }
    
    /// Returns the oldest child's age, if any
    var oldestChildAge: Int? {
        childrenAges.max()
    }
    
    /// Returns true if family has toddlers (under 3)
    var hasToddlers: Bool {
        childrenAges.contains { $0 < 3 }
    }
    
    /// Returns true if family has school-age children (5-12)
    var hasSchoolAgeChildren: Bool {
        childrenAges.contains { $0 >= 5 && $0 <= 12 }
    }
    
    /// Returns true if family has teenagers (13-17)
    var hasTeenagers: Bool {
        childrenAges.contains { $0 >= 13 && $0 <= 17 }
    }
    
    /// Returns recommended serving size multiplier based on family composition
    var servingSizeMultiplier: Double {
        var multiplier = Double(memberCount)
        
        // Adjust for children (they typically eat smaller portions)
        for age in childrenAges {
            if age < 3 {
                multiplier -= 0.7 // Toddlers eat much less
            } else if age < 8 {
                multiplier -= 0.5 // Young children eat less
            } else if age < 13 {
                multiplier -= 0.3 // Older children eat slightly less
            }
            // Teenagers (13+) eat adult portions or more, so no adjustment
        }
        
        return max(1.0, multiplier) // Ensure at least 1 serving
    }
    
    /// Returns family description for recipe context
    var familyDescription: String {
        var description = "\(memberCount) member"
        if memberCount > 1 {
            description += "s"
        }
        
        if hasChildren {
            let childCount = childrenAges.count
            description += " including \(childCount) child"
            if childCount > 1 {
                description += "ren"
            }
            
            if let youngest = youngestChildAge, let oldest = oldestChildAge {
                if youngest == oldest {
                    description += " (age \(youngest))"
                } else {
                    description += " (ages \(youngest)-\(oldest))"
                }
            }
        }
        
        return description
    }
}

/// Predefined family types for quick setup
enum FamilyType: String, CaseIterable, Sendable {
    case single = "Single Person"
    case couple = "Couple"
    case smallFamily = "Small Family (3-4 people)"
    case largeFamily = "Large Family (5+ people)"
    case withToddlers = "Family with Toddlers"
    case withTeenagers = "Family with Teenagers"
    
    var defaultFamilyInfo: FamilyInfo {
        switch self {
        case .single:
            return FamilyInfo(memberCount: 1)
        case .couple:
            return FamilyInfo(memberCount: 2)
        case .smallFamily:
            return FamilyInfo(memberCount: 4, hasChildren: true, childrenAges: [8, 10])
        case .largeFamily:
            return FamilyInfo(memberCount: 6, hasChildren: true, childrenAges: [5, 8, 12, 15])
        case .withToddlers:
            return FamilyInfo(memberCount: 3, hasChildren: true, childrenAges: [2])
        case .withTeenagers:
            return FamilyInfo(memberCount: 4, hasChildren: true, childrenAges: [14, 16])
        }
    }
    
    var description: String {
        switch self {
        case .single:
            return "Cooking for one person"
        case .couple:
            return "Cooking for two adults"
        case .smallFamily:
            return "Family with young children"
        case .largeFamily:
            return "Large family with multiple children"
        case .withToddlers:
            return "Family with toddlers (special meal considerations)"
        case .withTeenagers:
            return "Family with teenagers (larger portions)"
        }
    }
}
