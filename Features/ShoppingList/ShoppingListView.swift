import SwiftUI

struct ShoppingListView: View {
    @StateObject private var viewModel: ShoppingListViewModel
    @EnvironmentObject var shoppingListService: ShoppingListService
    
    init(shoppingListService: ShoppingListService) {
        _viewModel = StateObject(wrappedValue: ShoppingListViewModel(shoppingListService: shoppingListService))
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if viewModel.shoppingItems.isEmpty {
                    EmptyShoppingListView()
                } else {
                    List {
                        ForEach(viewModel.shoppingItems.indices, id: \.self) { index in
                            ShoppingListItemRow(
                                item: viewModel.shoppingItems[index],
                                onToggle: { viewModel.toggleItem(at: index) }
                            )
                        }
                        .onDelete(perform: viewModel.deleteItem)
                    }
                    .listStyle(InsetGroupedListStyle())
                    
                    if viewModel.hasCheckedItems {
                        Button(action: viewModel.clearCheckedItems) {
                            Text("Clear Checked Items")
                                .font(.body.weight(.medium))
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(Color.red)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("Shopping List")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    if viewModel.uncheckedItemsCount > 0 {
                        Text("\(viewModel.uncheckedItemsCount)")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.orange)
                            .foregroundColor(.white)
                            .clipShape(Capsule())
                    }
                }
            }
        }
    }
}

struct ShoppingListItemRow: View {
    let item: ShoppingItem
    let onToggle: () -> Void
    
    var body: some View {
        HStack {
            Button(action: onToggle) {
                Image(systemName: item.isChecked ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(item.isChecked ? .green : .gray)
                    .font(.title2)
            }
            .buttonStyle(PlainButtonStyle())
            
            Text(item.name)
                .font(.body)
                .strikethrough(item.isChecked)
                .foregroundColor(item.isChecked ? .secondary : .primary)
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct EmptyShoppingListView: View {
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            Image(systemName: "list.bullet")
                .font(.system(size: 80))
                .foregroundColor(.gray)
            Text("Your shopping list is empty")
                .font(.title2.weight(.semibold))
            Text("Add missing ingredients from recipe suggestions")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            Spacer()
        }
        .padding()
    }
} 