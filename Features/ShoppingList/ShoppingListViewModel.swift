import Foundation
import SwiftUI

@MainActor
class ShoppingListViewModel: ObservableObject {
    private let shoppingListService: ShoppingListService
    
    init(shoppingListService: ShoppingListService) {
        self.shoppingListService = shoppingListService
    }
    
    var shoppingItems: [ShoppingItem] {
        shoppingListService.shoppingItems
    }
    
    var uncheckedItemsCount: Int {
        shoppingListService.uncheckedItemsCount
    }
    
    var hasCheckedItems: Bool {
        shoppingItems.contains { $0.isChecked }
    }
    
    func toggleItem(at index: Int) {
        shoppingListService.toggleItem(at: index)
    }
    
    func deleteItem(at offsets: IndexSet) {
        shoppingListService.deleteItem(at: offsets)
    }
    
    func clearCheckedItems() {
        shoppingListService.clearCheckedItems()
    }
} 