import SwiftUI

struct PantryView: View {
    @EnvironmentObject var pantryService: PantryService
    
    var body: some View {
        PantryContentView(pantryService: pantryService)
    }
}

struct PantryContentView: View {
    @StateObject private var viewModel: PantryViewModel
    let pantryService: PantryService

    init(pantryService: PantryService) {
        self.pantryService = pantryService
        _viewModel = StateObject(wrappedValue: PantryViewModel(pantryService: pantryService))
    }

    var body: some View {
        NavigationView {
            VStack {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                    TextField("Search ingredients...", text: $viewModel.searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)
                .padding(.horizontal)

                VStack {
                    // Bulk Action Controls (shown in edit mode)
                    if viewModel.isEditMode && !pantryService.pantryItems.isEmpty {
                        bulkActionControls
                    }

                    List {
                        if pantryService.pantryItems.isEmpty {
                            emptyPantryView
                        } else {
                            // Recently Added Section
                            if !viewModel.recentlyAddedItems.isEmpty && !viewModel.isEditMode {
                                Section(header:
                                    HStack {
                                        Image(systemName: "star.fill")
                                            .foregroundColor(.orange)
                                            .font(.caption)
                                        Text("Recently Added")
                                            .font(.headline)
                                    }
                                ) {
                                    ForEach(viewModel.recentlyAddedItems) { ingredient in
                                        ingredientRow(ingredient: ingredient, isRecentlyAdded: true)
                                    }
                                }
                            }

                            // Categorized Items
                            ForEach(viewModel.categorizedPantryItems, id: \.category) { categoryGroup in
                                Section(header: categoryHeader(for: categoryGroup.category, count: categoryGroup.ingredients.count)) {
                                    ForEach(categoryGroup.ingredients) { ingredient in
                                        ingredientRow(ingredient: ingredient, isRecentlyAdded: false)
                                    }
                                    .onDelete { offsets in
                                        if !viewModel.isEditMode {
                                            deleteItems(from: categoryGroup.category, at: offsets)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .listStyle(InsetGroupedListStyle())
            }
            .navigationTitle("My Pantry")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                leading: viewModel.isEditMode ?
                    Button("Cancel") {
                        viewModel.toggleEditMode()
                    } : nil,
                trailing: HStack {
                    if !pantryService.pantryItems.isEmpty {
                        Button(viewModel.isEditMode ? "Done" : "Edit") {
                            viewModel.toggleEditMode()
                        }
                    }

                    Button(action: {
                        viewModel.showingAddIngredient = true
                    }) {
                        Image(systemName: "plus")
                            .font(.title2)
                    }
                }
            )
        }
        .sheet(isPresented: $viewModel.showingAddIngredient) {
            AddToPantryView(viewModel: viewModel)
        }
        .sheet(isPresented: $viewModel.showingEditIngredient) {
            EditIngredientView(viewModel: viewModel)
        }
    }
    
    private func deleteItems(from category: PantryCategory, at offsets: IndexSet) {
        // Get the ingredients for this category
        let ingredientsInCategory = viewModel.categorizedPantryItems
            .first(where: { $0.category == category })?.ingredients ?? []

        // Delete each ingredient at the given offsets
        for offset in offsets {
            if offset < ingredientsInCategory.count {
                let ingredient = ingredientsInCategory[offset]
                viewModel.deleteIngredient(ingredient, from: category)
            }
        }
    }

    // MARK: - Helper Views

    private var emptyPantryView: some View {
        VStack(spacing: 16) {
            Image(systemName: "cabinet")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.5))

            Text("Your pantry is empty")
                .font(.title2.weight(.medium))
                .foregroundColor(.secondary)

            Text("Start by scanning ingredients or add them manually")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button(action: {
                viewModel.showingAddIngredient = true
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add First Ingredient")
                }
                .font(.headline)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .center)
    }

    private func categoryHeader(for category: PantryCategory, count: Int) -> some View {
        HStack {
            Text(category.icon)
                .font(.title3)
            Text(category.rawValue)
                .font(.headline)
            Spacer()
        }
    }

    private func ingredientRow(ingredient: Ingredient, isRecentlyAdded: Bool) -> some View {
        HStack {
            // Selection checkbox in edit mode
            if viewModel.isEditMode {
                Button(action: {
                    viewModel.toggleItemSelection(ingredient)
                }) {
                    Image(systemName: viewModel.isItemSelected(ingredient) ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(viewModel.isItemSelected(ingredient) ? .blue : .gray)
                        .font(.title3)
                }
                .buttonStyle(PlainButtonStyle())
            }

            Text(ingredient.name)
                .font(.body)
                .foregroundColor(isRecentlyAdded ? .blue : .primary)

            Spacer()

            if isRecentlyAdded && !viewModel.isEditMode {
                Text("NEW")
                    .font(.caption2.weight(.bold))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.2))
                    .foregroundColor(.blue)
                    .cornerRadius(4)
            }

            // Edit button in normal mode
            if !viewModel.isEditMode {
                Button(action: {
                    viewModel.startEditingIngredient(ingredient)
                }) {
                    Image(systemName: "pencil")
                        .foregroundColor(.gray)
                        .font(.caption)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
    }

    private var bulkActionControls: some View {
        VStack(spacing: 12) {
            HStack {
                Text("\(viewModel.selectedItems.count) selected")
                    .font(.headline)
                    .foregroundColor(.secondary)

                Spacer()

                Button("Select All") {
                    viewModel.selectAllItems()
                }
                .disabled(viewModel.selectedItems.count == viewModel.filteredPantryItems.count)

                Button("Deselect All") {
                    viewModel.deselectAllItems()
                }
                .disabled(viewModel.selectedItems.isEmpty)
            }

            if !viewModel.selectedItems.isEmpty {
                Button(action: {
                    viewModel.deleteSelectedItems()
                }) {
                    HStack {
                        Image(systemName: "trash")
                        Text("Delete Selected (\(viewModel.selectedItems.count))")
                    }
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.red)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.1))
    }
}

// MARK: - Add to Pantry View

struct AddToPantryView: View {
    @ObservedObject var viewModel: PantryViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("New Ingredient")) {
                    TextField("Ingredient name", text: $viewModel.newIngredientName)
                        .focused($isFocused)
                        .onSubmit {
                            viewModel.addIngredient()
                            dismiss()
                        }
                }

                Section(header: Text("Category")) {
                    Picker("Category", selection: $viewModel.selectedCategory) {
                        ForEach(PantryCategory.allCases, id: \.self) { category in
                            HStack {
                                Text(category.icon)
                                Text(category.rawValue)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                }
            }
            .navigationTitle("Add Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    viewModel.cancelAddIngredient()
                    dismiss()
                },
                trailing: Button("Add") {
                    viewModel.addIngredient()
                    dismiss()
                }
                .disabled(viewModel.newIngredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        }
        .onAppear {
            isFocused = true
        }
    }
}

// MARK: - Edit Ingredient View

struct EditIngredientView: View {
    @ObservedObject var viewModel: PantryViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Edit Ingredient")) {
                    TextField("Ingredient name", text: $viewModel.editIngredientName)
                        .focused($isFocused)
                        .onSubmit {
                            viewModel.saveEditedIngredient()
                            dismiss()
                        }
                }

                Section(header: Text("Category")) {
                    Picker("Category", selection: $viewModel.editIngredientCategory) {
                        ForEach(PantryCategory.allCases, id: \.self) { category in
                            HStack {
                                Text(category.icon)
                                Text(category.rawValue)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                }
            }
            .navigationTitle("Edit Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    viewModel.cancelEditIngredient()
                    dismiss()
                },
                trailing: Button("Save") {
                    viewModel.saveEditedIngredient()
                    dismiss()
                }
                .disabled(viewModel.editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        }
        .onAppear {
            isFocused = true
        }
    }
}