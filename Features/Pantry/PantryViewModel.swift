import Foundation
import SwiftUI

@MainActor
class PantryViewModel: ObservableObject {
    private let pantryService: PantryService
    @Published var searchText: String = ""
    @Published var showingAddIngredient = false
    @Published var newIngredientName = ""
    @Published var selectedCategory: PantryCategory = .other
    @Published var isEditMode = false
    @Published var selectedItems: Set<UUID> = []
    @Published var showingEditIngredient = false
    @Published var editingIngredient: Ingredient?
    @Published var editIngredientName = ""
    @Published var editIngredientCategory: PantryCategory = .other

    // Computed property to get recently added items
    var recentlyAddedItems: [Ingredient] {
        return pantryService.pantryItems.filter { pantryService.isRecentlyAdded($0) }
    }

    // Computed property to get filtered items based on search
    var filteredPantryItems: [Ingredient] {
        if searchText.isEmpty {
            return pantryService.pantryItems
        } else {
            return pantryService.pantryItems.filter {
                $0.name.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // Computed property to group pantry items by category
    var categorizedPantryItems: [(category: PantryCategory, ingredients: [Ingredient])] {
        let grouped = Dictionary(grouping: filteredPantryItems) { $0.category }

        // Sort categories in a logical order (same as ResultsViewModel)
        let categoryOrder: [PantryCategory] = [
            .produce,
            .proteins,
            .dairyAndAlternatives,
            .bakingAndSweeteners,
            .pastry,
            .oilsVinegarsAndCondiments,
            .spicesAndSeasonings,
            .dryGoods,
            .packagedFoods,
            .snacksAndBeverages,
            .other
        ]

        return categoryOrder.compactMap { category in
            guard let items = grouped[category], !items.isEmpty else { return nil }
            return (category: category, ingredients: items)
        }
    }

    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }
    
    func deleteIngredient(_ ingredient: Ingredient, from category: PantryCategory) {
        if let index = pantryService.pantryItems.firstIndex(where: { $0.id == ingredient.id }) {
            pantryService.pantryItems.remove(at: index)
        }
    }

    // MARK: - Manual Add Functionality

    func addIngredient() {
        let trimmedName = newIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            let newIngredient = Ingredient(name: trimmedName, category: selectedCategory)
            pantryService.addIngredient(newIngredient)
            newIngredientName = ""
            selectedCategory = .other
            showingAddIngredient = false
        }
    }

    func cancelAddIngredient() {
        newIngredientName = ""
        selectedCategory = .other
        showingAddIngredient = false
    }

    // MARK: - Edit Mode & Bulk Actions

    func toggleEditMode() {
        isEditMode.toggle()
        if !isEditMode {
            selectedItems.removeAll()
        }
    }

    func toggleItemSelection(_ ingredient: Ingredient) {
        if selectedItems.contains(ingredient.id) {
            selectedItems.remove(ingredient.id)
        } else {
            selectedItems.insert(ingredient.id)
        }
    }

    func isItemSelected(_ ingredient: Ingredient) -> Bool {
        return selectedItems.contains(ingredient.id)
    }

    func selectAllItems() {
        selectedItems = Set(filteredPantryItems.map { $0.id })
    }

    func deselectAllItems() {
        selectedItems.removeAll()
    }

    func deleteSelectedItems() {
        let itemsToDelete = filteredPantryItems.filter { selectedItems.contains($0.id) }
        for item in itemsToDelete {
            pantryService.deleteIngredient(item)
        }
        selectedItems.removeAll()
        isEditMode = false
    }

    // MARK: - Edit Individual Item

    func startEditingIngredient(_ ingredient: Ingredient) {
        editingIngredient = ingredient
        editIngredientName = ingredient.name
        editIngredientCategory = ingredient.category
        showingEditIngredient = true
    }

    func saveEditedIngredient() {
        guard let ingredient = editingIngredient else { return }

        let trimmedName = editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            // Find and update the ingredient
            if let index = pantryService.pantryItems.firstIndex(where: { $0.id == ingredient.id }) {
                pantryService.pantryItems[index].name = trimmedName
                pantryService.pantryItems[index].category = editIngredientCategory
            }
        }

        cancelEditIngredient()
    }

    func cancelEditIngredient() {
        editingIngredient = nil
        editIngredientName = ""
        editIngredientCategory = .other
        showingEditIngredient = false
    }
} 