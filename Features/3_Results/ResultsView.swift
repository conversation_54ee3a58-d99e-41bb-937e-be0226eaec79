import SwiftUI

struct ResultsView: View {
    @StateObject var viewModel: ResultsViewModel
    
    var body: some View {
        NavigationView {
            VStack {
                if viewModel.showingSuccessState {
                    successStateView
                } else if viewModel.ingredients.isEmpty {
                    EmptyIngredientsView()
                } else {
                    List {
                        ForEach(viewModel.categorizedIngredients, id: \.category) { categoryGroup in
                            Section(header:
                                HStack {
                                    Button(action: {
                                        viewModel.toggleCategorySelection(categoryGroup.category)
                                    }) {
                                        HStack {
                                            Image(systemName: viewModel.isCategoryFullySelected(categoryGroup.category) ? "checkmark.square.fill" : "square")
                                                .foregroundColor(viewModel.isCategoryFullySelected(categoryGroup.category) ? .green : .gray)
                                                .font(.title3)

                                            Text(categoryGroup.category.icon)
                                                .font(.title3)
                                            Text(categoryGroup.category.rawValue)
                                                .font(.headline)
                                        }
                                    }
                                    .buttonStyle(PlainButtonStyle())

                                    Spacer()
                                    Text("\(categoryGroup.ingredients.count)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 2)
                                        .background(Color.gray.opacity(0.2))
                                        .clipShape(Capsule())
                                }
                                .padding(.vertical, 2)
                            ) {
                                ForEach(categoryGroup.ingredients) { ingredient in
                                    HStack {
                                        // Selection checkbox
                                        Button(action: {
                                            viewModel.toggleIngredient(ingredient)
                                        }) {
                                            Image(systemName: ingredient.isSelected ? "checkmark.square.fill" : "square")
                                                .foregroundColor(ingredient.isSelected ? .green : .gray)
                                                .font(.title2)
                                        }
                                        .buttonStyle(PlainButtonStyle())

                                        // Ingredient name
                                        Text(ingredient.name)
                                            .font(.body)
                                            .strikethrough(!ingredient.isSelected)
                                            .foregroundColor(ingredient.isSelected ? .primary : .secondary)

                                        Spacer()

                                        // Edit button (same as pantry)
                                        Button(action: {
                                            viewModel.startEditingIngredient(ingredient)
                                        }) {
                                            Image(systemName: "pencil")
                                                .foregroundColor(.gray)
                                                .font(.caption)
                                        }
                                        .buttonStyle(PlainButtonStyle())
                                    }
                                    .padding(.vertical, 4)
                                }
                            }
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                    
                    VStack(spacing: 15) {
                        // Smart Selection Controls
                        HStack(spacing: 12) {
                            Button(action: { viewModel.selectAll() }) {
                                Text("Select All")
                                    .font(.caption.weight(.medium))
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.blue.opacity(0.1))
                                    .foregroundColor(.blue)
                                    .cornerRadius(8)
                            }

                            Button(action: { viewModel.deselectAll() }) {
                                Text("Deselect All")
                                    .font(.caption.weight(.medium))
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.gray.opacity(0.1))
                                    .foregroundColor(.gray)
                                    .cornerRadius(8)
                            }

                            Spacer()
                        }

                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                viewModel.addToPantry()
                            }
                        }) {
                            HStack {
                                Image(systemName: viewModel.hasAddedToPantry ? "checkmark.circle.fill" : "tray.and.arrow.down.fill")
                                    .scaleEffect(viewModel.hasAddedToPantry ? 1.2 : 1.0)
                                    .animation(.spring(response: 0.3), value: viewModel.hasAddedToPantry)
                                Text(viewModel.hasAddedToPantry ? "Added to Pantry" : "Add to Pantry (\(viewModel.getSelectedIngredients().count))")
                            }
                            .font(.title3.weight(.medium))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(viewModel.hasAddedToPantry ? Color.green : Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            .scaleEffect(viewModel.hasAddedToPantry ? 1.05 : 1.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: viewModel.hasAddedToPantry)
                        }
                        .disabled(viewModel.getSelectedIngredients().isEmpty || viewModel.hasAddedToPantry)
                        
                        Text("\(viewModel.getSelectedIngredients().count) of \(viewModel.ingredients.count) ingredients selected")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .navigationTitle("Ingredients Found")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                trailing: Button("Rescan") {
                    viewModel.takeNewPhoto()
                }
            )
        }
        .alert("No Ingredients Detected", isPresented: $viewModel.showingNoIngredientsAlert) {
            Button("Rescan") {
                viewModel.takeNewPhoto()
            }
        } message: {
            Text("We couldn't find any food items in your image. Please try again.")
        }
        .sheet(isPresented: $viewModel.showingEditIngredient) {
            ResultsEditIngredientView(viewModel: viewModel)
        }
    }

    // MARK: - Helper Views

    private var successStateView: some View {
        VStack(spacing: 24) {
            Spacer()

            // Success Animation
            VStack(spacing: 20) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.green)
                    .scaleEffect(viewModel.showingSuccessState ? 1.0 : 0.3)
                    .opacity(viewModel.showingSuccessState ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.7), value: viewModel.showingSuccessState)

                VStack(spacing: 8) {
                    Text("Added \(viewModel.addedItemsCount) items to pantry")
                        .font(.title2.weight(.semibold))
                        .foregroundColor(.green)
                        .opacity(viewModel.showingSuccessState ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.5).delay(0.2), value: viewModel.showingSuccessState)

                    Text("Taking you to your pantry...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .opacity(viewModel.showingSuccessState ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.5).delay(0.4), value: viewModel.showingSuccessState)
                }
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            Color.green.opacity(0.05)
                .opacity(viewModel.showingSuccessState ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.3), value: viewModel.showingSuccessState)
        )
    }
}

struct EmptyIngredientsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            Image(systemName: "tray")
                .font(.system(size: 80))
                .foregroundColor(.gray)
            Text("No ingredients found")
                .font(.title2.weight(.semibold))
            Text("Try taking a clearer photo of your ingredients")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            Spacer()
        }
        .padding()
    }
}

// MARK: - Edit Ingredient View for Results

struct ResultsEditIngredientView: View {
    @ObservedObject var viewModel: ResultsViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Edit Ingredient")) {
                    TextField("Ingredient name", text: $viewModel.editIngredientName)
                        .focused($isFocused)
                        .onSubmit {
                            viewModel.saveEditedIngredient()
                            dismiss()
                        }
                }

                Section(header: Text("Category")) {
                    Picker("Category", selection: $viewModel.editIngredientCategory) {
                        ForEach(PantryCategory.allCases, id: \.self) { category in
                            HStack {
                                Text(category.icon)
                                Text(category.rawValue)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                }
            }
            .navigationTitle("Edit Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    viewModel.cancelEditIngredient()
                    dismiss()
                },
                trailing: Button("Save") {
                    viewModel.saveEditedIngredient()
                    dismiss()
                }
                .disabled(viewModel.editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        }
        .onAppear {
            isFocused = true
        }
    }
}