import SwiftUI

@MainActor
class ResultsViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    private let pantryService: PantryService
    @Published var ingredients: [Ingredient]
    @Published var showingNoIngredientsAlert = false
    @Published var hasAddedToPantry = false
    @Published var showingSuccessState = false
    @Published var addedItemsCount = 0
    @Published var showingEditIngredient = false
    @Published var editingIngredient: Ingredient?
    @Published var editIngredientName = ""
    @Published var editIngredientCategory: PantryCategory = .other
    
    // Computed property to group ingredients by category
    var categorizedIngredients: [(category: PantryCategory, ingredients: [Ingredient])] {
        let grouped = Dictionary(grouping: ingredients) { $0.category }
        
        // Sort categories in a logical order
        let categoryOrder: [PantryCategory] = [
            .produce,
            .proteins,
            .dairyAndAlternatives,
            .bakingAndSweeteners,
            .pastry,
            .oilsVinegarsAndCondiments,
            .spicesAndSeasonings,
            .dryGoods,
            .packagedFoods,
            .snacksAndBeverages,
            .other
        ]
        
        return categoryOrder.compactMap { category in
            guard let items = grouped[category], !items.isEmpty else { return nil }
            return (category: category, ingredients: items)
        }
    }
    
    init(coordinator: AppCoordinator, ingredients: [Ingredient], pantryService: PantryService) {
        self.coordinator = coordinator
        self.ingredients = ingredients
        self.pantryService = pantryService
        
        // Check if no ingredients were found
        if ingredients.isEmpty {
            showingNoIngredientsAlert = true
        }
    }
    
    func toggleIngredient(at index: Int) {
        ingredients[index].isSelected.toggle()
    }
    
    func toggleIngredient(_ ingredient: Ingredient) {
        if let index = ingredients.firstIndex(where: { $0.id == ingredient.id }) {
            ingredients[index].isSelected.toggle()
        }
    }
    

    
    func takeNewPhoto() {
        coordinator?.navigateToStaging()
    }
    
    func getSelectedIngredients() -> [Ingredient] {
        ingredients.filter { $0.isSelected }
    }
    
    func addToPantry() {
        let selectedIngredients = getSelectedIngredients()
        if !selectedIngredients.isEmpty {
            addedItemsCount = selectedIngredients.count
            pantryService.addIngredients(selectedIngredients)
            hasAddedToPantry = true

            // Store recently added items for highlighting
            pantryService.markAsRecentlyAdded(selectedIngredients)

            // Show success state with animation
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                showingSuccessState = true
            }

            // Navigate to pantry and reset scan tab after delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
                self?.coordinator?.switchToPantryTabAndResetScan()
            }
        }
    }

    // MARK: - Smart Selection Methods

    func selectAll() {
        for i in ingredients.indices {
            ingredients[i].isSelected = true
        }
    }

    func deselectAll() {
        for i in ingredients.indices {
            ingredients[i].isSelected = false
        }
    }

    func toggleCategorySelection(_ category: PantryCategory) {
        let categoryIngredients = ingredients.filter { $0.category == category }
        let allSelected = categoryIngredients.allSatisfy { $0.isSelected }

        for i in ingredients.indices {
            if ingredients[i].category == category {
                ingredients[i].isSelected = !allSelected
            }
        }
    }

    func isCategoryFullySelected(_ category: PantryCategory) -> Bool {
        let categoryIngredients = ingredients.filter { $0.category == category }
        return !categoryIngredients.isEmpty && categoryIngredients.allSatisfy { $0.isSelected }
    }

    // MARK: - Edit Individual Ingredient

    func startEditingIngredient(_ ingredient: Ingredient) {
        editingIngredient = ingredient
        editIngredientName = ingredient.name
        editIngredientCategory = ingredient.category
        showingEditIngredient = true
    }

    func saveEditedIngredient() {
        guard let ingredient = editingIngredient else { return }

        let trimmedName = editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            // Find and update the ingredient in the results array
            if let index = ingredients.firstIndex(where: { $0.id == ingredient.id }) {
                ingredients[index].name = trimmedName
                ingredients[index].category = editIngredientCategory
            }
        }

        cancelEditIngredient()
    }

    func cancelEditIngredient() {
        editingIngredient = nil
        editIngredientName = ""
        editIngredientCategory = .other
        showingEditIngredient = false
    }
} 