import SwiftUI
import UIKit

@MainActor
class BatchGeminiProcessingViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    private let images: [UIImage]
    private let visionResponses: [String]
    private let geminiService = GeminiAPIService()
    
    @Published var isLoading = true
    @Published var errorMessage: String?
    
    init(coordinator: AppCoordinator, images: [UIImage], visionResponses: [String]) {
        self.coordinator = coordinator
        self.images = images
        self.visionResponses = visionResponses
        Task {
            await processWithGemini()
        }
    }
    
    private func processWithGemini() async {
        do {
            // Aggregate all vision responses into one text
            let aggregatedText = visionResponses
                .enumerated()
                .map { index, text in
                    "=== Image \(index + 1) ===\n\(text)"
                }
                .joined(separator: "\n\n")
            
            // Call Gemini API with aggregated text
            let ingredients = try await geminiService.extractIngredients(from: aggregatedText)
            
            // Check if no ingredients found
            if ingredients.isEmpty {
                coordinator?.navigateToResults(ingredients: ingredients)
                return
            }
            
            // Remove duplicates while preserving order and categories
            let uniqueIngredients = ingredients.reduce(into: [Ingredient]()) { result, ingredient in
                if !result.contains(where: { $0.name.caseInsensitiveCompare(ingredient.name) == .orderedSame }) {
                    result.append(ingredient)
                }
            }
            
            #if DEBUG
            // In debug mode, navigate to debug view first
            // Convert ingredients to string for debug display
            let geminiResponse = uniqueIngredients.map { "\($0.name) (\($0.category.rawValue))" }.joined(separator: ", ")
            coordinator?.navigateToDebug(visionResponse: aggregatedText, geminiResponse: geminiResponse, ingredients: uniqueIngredients)
            #else
            // In release mode, go directly to results
            coordinator?.navigateToResults(ingredients: uniqueIngredients)
            #endif
            
        } catch {
            errorMessage = error.localizedDescription
            // Navigate back to staging on error
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                self?.coordinator?.navigateToStaging()
            }
        }
        
        isLoading = false
    }
} 