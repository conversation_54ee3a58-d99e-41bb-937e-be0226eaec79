import SwiftUI

#if DEBUG
struct DebugView: View {
    @StateObject var viewModel: DebugViewModel
    
    var body: some View {
        NavigationView {
            VStack {
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Raw Google Vision API Output:")
                                .font(.headline.weight(.bold))
                            
                            Text(viewModel.visionResponse)
                                .font(.system(.body, design: .monospaced))
                                .padding()
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(8)
                        }
                        
                        Divider()
                        
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Cleaned Gemini API Output:")
                                .font(.headline.weight(.bold))

                            Text(viewModel.geminiResponse)
                                .font(.system(.body, design: .monospaced))
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(8)
                        }

                        Divider()

                        VStack(alignment: .leading, spacing: 10) {
                            Text("🔥 Firebase Configuration Status:")
                                .font(.headline.weight(.bold))

                            Text(firebaseConfigStatus())
                                .font(.system(.caption, design: .monospaced))
                                .padding()
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                }
                
                Button(action: viewModel.continueToResults) {
                    Text("Continue to Results")
                        .font(.title2.weight(.semibold))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding()
            }
            .navigationTitle("Debug Info")
            .navigationBarTitleDisplayMode(.large)
        }
    }

    private func firebaseConfigStatus() -> String {
        var status = "🔍 Firebase Configuration Check:\n\n"

        // Check if GoogleService-Info.plist exists
        if let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist") {
            status += "✅ GoogleService-Info.plist found\n"
            status += "📍 Path: \(path)\n\n"

            // Try to read the plist
            if let plist = NSDictionary(contentsOfFile: path) {
                let bundleId = plist["BUNDLE_ID"] as? String ?? "Unknown"
                let projectId = plist["PROJECT_ID"] as? String ?? "Unknown"
                let clientId = plist["CLIENT_ID"] as? String ?? "Unknown"

                status += "📋 Configuration Details:\n"
                status += "• Bundle ID: \(bundleId)\n"
                status += "• Project ID: \(projectId)\n"
                status += "• Client ID: \(clientId.prefix(20))...\n\n"

                // Check if Bundle ID matches current app
                let currentBundleId = Bundle.main.bundleIdentifier ?? "Unknown"
                if bundleId == currentBundleId {
                    status += "✅ Bundle ID matches current app\n"
                    status += "• App Bundle ID: \(currentBundleId)\n"
                    status += "• Config Bundle ID: \(bundleId)\n"
                } else {
                    status += "❌ Bundle ID mismatch!\n"
                    status += "• App Bundle ID: \(currentBundleId)\n"
                    status += "• Config Bundle ID: \(bundleId)\n"
                }
            } else {
                status += "❌ Failed to read GoogleService-Info.plist\n"
            }
        } else {
            status += "❌ GoogleService-Info.plist not found\n"
        }

        return status
    }
}
#endif 