import SwiftUI

#if DEBUG
@MainActor
class DebugViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    let visionResponse: String
    let geminiResponse: String
    let ingredients: [Ingredient]

    init(coordinator: AppCoordinator, visionResponse: String, geminiResponse: String, ingredients: [Ingredient]) {
        self.coordinator = coordinator
        self.visionResponse = visionResponse
        self.geminiResponse = geminiResponse
        self.ingredients = ingredients
    }

    func continueToResults() {
        // Use the properly categorized ingredients passed from Gemini processing
        coordinator?.navigateToResults(ingredients: ingredients)
    }
}
#endif 