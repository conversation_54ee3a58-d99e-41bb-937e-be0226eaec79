import Foundation
import SwiftUI

@MainActor
class RecipeGeneratorViewModel: ObservableObject {
    @Published var cookingTimeInMinutes = 30
    @Published var numberOfServings = 2
    @Published var dietaryRestrictions: [String: Bool] = [
        "Vegetarian": false,
        "Vegan": false,
        "Gluten-Free": false,
        "Dairy-Free": false,
        "Nut-Free": false,
        "Low-Carb": false
    ]
    @Published var isGenerating = false
    @Published var generatedRecipeIdeas: [RecipeIdea] = []
    @Published var showError = false
    @Published var errorMessage = ""
    
    private let recipeService = ServiceContainer.shared.recipeGenerationService

    // Available cooking time options
    let cookingTimeOptions = [15, 30, 45, 60]
    
    var selectedRestrictions: [String] {
        dietaryRestrictions.compactMap { key, value in
            value ? key : nil
        }
    }
    
    var canGenerateRecipe: Bool {
        !ServiceContainer.shared.pantryService.pantryItems.isEmpty && !isGenerating
    }
    
    func generateMealIdeas() async {
        guard canGenerateRecipe else { return }
        
        isGenerating = true
        showError = false
        generatedRecipeIdeas = []
        
        let ingredients = ServiceContainer.shared.pantryService.pantryItems.map { $0.name }
        let preferences = RecipePreferences(
            cookingTimeInMinutes: cookingTimeInMinutes,
            numberOfServings: numberOfServings,
            dietaryRestrictions: selectedRestrictions
        )
        
        do {
            let recipes = try await recipeService.generateMealIdeas(
                from: ingredients,
                preferences: preferences
            )
            
            // Process recipes to determine status and missing ingredients
            generatedRecipeIdeas = processRecipes(recipes, pantryIngredients: ingredients)
        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }
        
        isGenerating = false
    }
    
    private func processRecipes(_ recipes: [Recipe], pantryIngredients: [String]) -> [RecipeIdea] {
        return recipes.map { recipe in
            let missingIngredients = findMissingIngredients(
                recipeIngredients: recipe.ingredients,
                pantryIngredients: pantryIngredients
            )
            
            let status: RecipeIdea.RecipeStatus = missingIngredients.isEmpty ? .readyToCook : .almostThere
            
            return RecipeIdea(
                recipe: recipe,
                status: status,
                missingIngredients: missingIngredients
            )
        }
    }
    
    private func findMissingIngredients(recipeIngredients: [String], pantryIngredients: [String]) -> [String] {
        let pantrySet = Set(pantryIngredients.map { $0.lowercased() })
        var missingIngredients: [String] = []
        
        // Common pantry staples that we don't consider as "missing"
        let commonStaples = Set([
            "salt", "pepper", "water", "oil", "olive oil", "vegetable oil",
            "butter", "sugar", "flour", "black pepper", "sea salt"
        ])
        
        for ingredient in recipeIngredients {
            // Extract the main ingredient name (removing quantities and measurements)
            let cleanedIngredient = extractIngredientName(from: ingredient).lowercased()
            
            // Skip if it's a common staple
            if commonStaples.contains(cleanedIngredient) {
                continue
            }
            
            // Check if ingredient is in pantry
            let isInPantry = pantrySet.contains { pantryItem in
                cleanedIngredient.contains(pantryItem) || pantryItem.contains(cleanedIngredient)
            }
            
            if !isInPantry {
                missingIngredients.append(ingredient)
            }
        }
        
        return missingIngredients
    }
    
    private func extractIngredientName(from fullIngredient: String) -> String {
        // Remove common measurements and quantities
        let patterns = [
            "\\d+\\s*(cups?|tbsp|tsp|tablespoons?|teaspoons?|oz|ounces?|lbs?|pounds?|g|grams?|kg|kilograms?|ml|milliliters?|l|liters?)",
            "\\d+/\\d+",
            "\\d+\\.?\\d*",
            "^(a |an |the )",
            "(, .*$)" // Remove anything after a comma
        ]
        
        var cleanedName = fullIngredient
        for pattern in patterns {
            cleanedName = cleanedName.replacingOccurrences(
                of: pattern,
                with: "",
                options: [.regularExpression, .caseInsensitive]
            )
        }
        
        return cleanedName.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    func addToShoppingList(_ ingredient: String) {
        ServiceContainer.shared.shoppingListService.addItem(ingredient)
    }
    
    func toggleRestriction(_ restriction: String) {
        dietaryRestrictions[restriction]?.toggle()
    }
} 