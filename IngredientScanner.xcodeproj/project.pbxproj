// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		16947ACBFFE296C4C5608521 /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = C093F1FD032970F0D124F474 /* ImagePicker.swift */; };
		190112BDFD568D7BD1682DCA /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 3C4267156C55BCA053A6D391 /* Assets.xcassets */; };
		2009917FD1AE77EF5C26151D /* PantryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */; };
		286D3AD60207B07E1922ED46 /* Recipe.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE22904B43C38F5DA2C1BCCE /* Recipe.swift */; };
		2A34768F4DFE82C7F478BA00 /* BatchVisionResultsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6E85C7B4410C026351001F5F /* BatchVisionResultsViewModel.swift */; };
		300FF772D541C1E8D839037D /* ResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58F7E951E15E903C3F2DA88E /* ResultsView.swift */; };
		3BF985B6E33165CCCAB97988 /* MultiImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */; };
		3CEEAA16C0A38AD1F62E30E0 /* PantryService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFC23B81C1836632A9C0C7D1 /* PantryService.swift */; };
		4110690D016E8E8C862C9D91 /* BatchGeminiProcessingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B1AC9555D7C1720FB75EFEC /* BatchGeminiProcessingView.swift */; };
		465AA90B58DBF295DF9729FB /* PantryCategory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */; };
		46D1AA5FBF4641ECC38CAB93 /* GeneratedRecipeDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */; };
		470B030CFE6F3D8EF8AF9C97 /* App.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C6BAB000A706022E5DE4311 /* App.swift */; };
		4CAC1DE461273018EEC02DA7 /* ResultsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */; };
		536BF110E8AD9928F8EA2EE1 /* PermissionHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2ECBC2D9E3F0886A6F5C4476 /* PermissionHandler.swift */; };
		561E006C2E34904100BFFB5D /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 561E006B2E34904100BFFB5D /* GoogleService-Info.plist */; };
		590EF66376A8348CBBBF4BCF /* GeminiAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */; };
		5ED14FC0EC3F3AA2E63FD535 /* APIKeys.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6E814F10D859D0594F1E3C2 /* APIKeys.swift */; };
		6C6A417E7F5E0972303323D6 /* DebugViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64498B16CC20397CD1568211 /* DebugViewModel.swift */; };
		7A3011B5F41A0480B47E8D1C /* StagingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D8597742EEEA039E4F43ED6 /* StagingView.swift */; };
		88D2F764123B52301A3B70B8 /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */; };
		89AFB66F259BE7565A5D1801 /* DebugView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79D1F4365F0010CABC1CADF7 /* DebugView.swift */; };
		8C6699499ED137FE3BBEDE00 /* StagingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B01392334341FB8E206C523 /* StagingViewModel.swift */; };
		91C3FDCFC68D219BBE62D9FD /* ShoppingListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6942D61D21D3C690316D1906 /* ShoppingListView.swift */; };
		9634C6A0A790DFCF8DB74308 /* PantryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E09DF5AB9B43F2B5940B387E /* PantryView.swift */; };
		98B4CE1CB107DFB949C39140 /* BatchProcessingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A0641497C2857F848AB5671 /* BatchProcessingViewModel.swift */; };
		9AAB43E55D0D950A9774FB0D /* RecipeCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B388B3CA95BA26238131006 /* RecipeCardView.swift */; };
		9AC0D19AA92FEA4636B57E8D /* Ingredient.swift in Sources */ = {isa = PBXBuildFile; fileRef = C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */; };
		A080BC4B38E2E67D909ECC85 /* BatchGeminiProcessingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0A5BD2661F93D5E9C2FE0497 /* BatchGeminiProcessingViewModel.swift */; };
		A39330ADD3AA43305DE381C4 /* BatchProcessingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6CE48888DEB17A4D41373CDE /* BatchProcessingView.swift */; };
		AB83BF354A5EDB9FB70257BE /* ShoppingListService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 701CB3D0687502DE6A6450B9 /* ShoppingListService.swift */; };
		C68C0438252E06DD37D44BC1 /* BatchVisionResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 585FFDA2DC1CDF724D74C47C /* BatchVisionResultsView.swift */; };
		C6EA4672D5D150BFFABE904E /* ShoppingListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 72E6A4F7543386F7A8B6967B /* ShoppingListViewModel.swift */; };
		E684CC98841467E1D92339FE /* GoogleVisionAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA27F2449FA133E6C7BD796D /* GoogleVisionAPIService.swift */; };
		E6D16E11F54A7B3AC607A39C /* RecipeGeneratorViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */; };
		EC9C0184D659F23C2A4D1112 /* RecipeGenerationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF40FDE398B1A6CE1FD2B0B7 /* RecipeGenerationService.swift */; };
		EDB1FCBE871A2FC42E1BB1B0 /* RecipeGeneratorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */; };
		F9A41FBACF9CCA4A42169055 /* ServiceContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CA085D99E48956579051C8A /* ServiceContainer.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0A5BD2661F93D5E9C2FE0497 /* BatchGeminiProcessingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchGeminiProcessingViewModel.swift; sourceTree = "<group>"; };
		0B01392334341FB8E206C523 /* StagingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StagingViewModel.swift; sourceTree = "<group>"; };
		0B1AC9555D7C1720FB75EFEC /* BatchGeminiProcessingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchGeminiProcessingView.swift; sourceTree = "<group>"; };
		11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiImagePicker.swift; sourceTree = "<group>"; };
		176398FEADBD4CF189075941 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneratedRecipeDetailView.swift; sourceTree = "<group>"; };
		2ECBC2D9E3F0886A6F5C4476 /* PermissionHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionHandler.swift; sourceTree = "<group>"; };
		3C4267156C55BCA053A6D391 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		3C6BAB000A706022E5DE4311 /* App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = App.swift; sourceTree = "<group>"; };
		3CA085D99E48956579051C8A /* ServiceContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServiceContainer.swift; sourceTree = "<group>"; };
		561E006B2E34904100BFFB5D /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		585FFDA2DC1CDF724D74C47C /* BatchVisionResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchVisionResultsView.swift; sourceTree = "<group>"; };
		58F7E951E15E903C3F2DA88E /* ResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsView.swift; sourceTree = "<group>"; };
		64498B16CC20397CD1568211 /* DebugViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebugViewModel.swift; sourceTree = "<group>"; };
		6942D61D21D3C690316D1906 /* ShoppingListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShoppingListView.swift; sourceTree = "<group>"; };
		6CE48888DEB17A4D41373CDE /* BatchProcessingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchProcessingView.swift; sourceTree = "<group>"; };
		6D8597742EEEA039E4F43ED6 /* StagingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StagingView.swift; sourceTree = "<group>"; };
		6E85C7B4410C026351001F5F /* BatchVisionResultsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchVisionResultsViewModel.swift; sourceTree = "<group>"; };
		701CB3D0687502DE6A6450B9 /* ShoppingListService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShoppingListService.swift; sourceTree = "<group>"; };
		72E6A4F7543386F7A8B6967B /* ShoppingListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShoppingListViewModel.swift; sourceTree = "<group>"; };
		79D1F4365F0010CABC1CADF7 /* DebugView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebugView.swift; sourceTree = "<group>"; };
		7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppCoordinator.swift; sourceTree = "<group>"; };
		84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryCategory.swift; sourceTree = "<group>"; };
		96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeminiAPIService.swift; sourceTree = "<group>"; };
		9A0641497C2857F848AB5671 /* BatchProcessingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchProcessingViewModel.swift; sourceTree = "<group>"; };
		9B388B3CA95BA26238131006 /* RecipeCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCardView.swift; sourceTree = "<group>"; };
		A6E814F10D859D0594F1E3C2 /* APIKeys.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIKeys.swift; sourceTree = "<group>"; };
		AE22904B43C38F5DA2C1BCCE /* Recipe.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Recipe.swift; sourceTree = "<group>"; };
		B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsViewModel.swift; sourceTree = "<group>"; };
		C093F1FD032970F0D124F474 /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePicker.swift; sourceTree = "<group>"; };
		C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Ingredient.swift; sourceTree = "<group>"; };
		CF40FDE398B1A6CE1FD2B0B7 /* RecipeGenerationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationService.swift; sourceTree = "<group>"; };
		D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = IngredientScanner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorView.swift; sourceTree = "<group>"; };
		D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryViewModel.swift; sourceTree = "<group>"; };
		DA27F2449FA133E6C7BD796D /* GoogleVisionAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoogleVisionAPIService.swift; sourceTree = "<group>"; };
		DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorViewModel.swift; sourceTree = "<group>"; };
		DFC23B81C1836632A9C0C7D1 /* PantryService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryService.swift; sourceTree = "<group>"; };
		E09DF5AB9B43F2B5940B387E /* PantryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXGroup section */
		1FC412429FB925EF376F80A6 /* 1_ImageCapture */ = {
			isa = PBXGroup;
			children = (
				6D8597742EEEA039E4F43ED6 /* StagingView.swift */,
				0B01392334341FB8E206C523 /* StagingViewModel.swift */,
			);
			path = 1_ImageCapture;
			sourceTree = "<group>";
		};
		2BF4550E1C98B14AB6FC9B39 /* 2_ImagePreview */ = {
			isa = PBXGroup;
			children = (
				0B1AC9555D7C1720FB75EFEC /* BatchGeminiProcessingView.swift */,
				0A5BD2661F93D5E9C2FE0497 /* BatchGeminiProcessingViewModel.swift */,
				6CE48888DEB17A4D41373CDE /* BatchProcessingView.swift */,
				9A0641497C2857F848AB5671 /* BatchProcessingViewModel.swift */,
				585FFDA2DC1CDF724D74C47C /* BatchVisionResultsView.swift */,
				6E85C7B4410C026351001F5F /* BatchVisionResultsViewModel.swift */,
			);
			path = 2_ImagePreview;
			sourceTree = "<group>";
		};
		3FB62EA05FC23BA62F42A20B /* ShoppingList */ = {
			isa = PBXGroup;
			children = (
				6942D61D21D3C690316D1906 /* ShoppingListView.swift */,
				72E6A4F7543386F7A8B6967B /* ShoppingListViewModel.swift */,
			);
			path = ShoppingList;
			sourceTree = "<group>";
		};
		40D60551B89A5B09AD08F353 /* RecipeGenerator */ = {
			isa = PBXGroup;
			children = (
				2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */,
				9B388B3CA95BA26238131006 /* RecipeCardView.swift */,
				D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */,
				DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */,
			);
			path = RecipeGenerator;
			sourceTree = "<group>";
		};
		75DE33128F7D036CC2DBC4AA /* 3_Results */ = {
			isa = PBXGroup;
			children = (
				58F7E951E15E903C3F2DA88E /* ResultsView.swift */,
				B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */,
			);
			path = 3_Results;
			sourceTree = "<group>";
		};
		777F36B8DA01C8AB0E61EDA1 /* Debug */ = {
			isa = PBXGroup;
			children = (
				79D1F4365F0010CABC1CADF7 /* DebugView.swift */,
				64498B16CC20397CD1568211 /* DebugViewModel.swift */,
			);
			path = Debug;
			sourceTree = "<group>";
		};
		8AF856DE78DD25E25BA3690A /* Pantry */ = {
			isa = PBXGroup;
			children = (
				E09DF5AB9B43F2B5940B387E /* PantryView.swift */,
				D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */,
			);
			path = Pantry;
			sourceTree = "<group>";
		};
		A1F9EAED5F5762EE342BAE4F /* Coordinator */ = {
			isa = PBXGroup;
			children = (
				7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */,
			);
			path = Coordinator;
			sourceTree = "<group>";
		};
		A3C06954CFBF169AC85CF7F6 /* Models */ = {
			isa = PBXGroup;
			children = (
				C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */,
				84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */,
				AE22904B43C38F5DA2C1BCCE /* Recipe.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		B4B9DBFF954BD5347C879108 /* Features */ = {
			isa = PBXGroup;
			children = (
				1FC412429FB925EF376F80A6 /* 1_ImageCapture */,
				2BF4550E1C98B14AB6FC9B39 /* 2_ImagePreview */,
				75DE33128F7D036CC2DBC4AA /* 3_Results */,
				777F36B8DA01C8AB0E61EDA1 /* Debug */,
				8AF856DE78DD25E25BA3690A /* Pantry */,
				40D60551B89A5B09AD08F353 /* RecipeGenerator */,
				3FB62EA05FC23BA62F42A20B /* ShoppingList */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		BBEFB2E0D465492D3CC1B9F4 /* Application */ = {
			isa = PBXGroup;
			children = (
				3C6BAB000A706022E5DE4311 /* App.swift */,
				3C4267156C55BCA053A6D391 /* Assets.xcassets */,
				176398FEADBD4CF189075941 /* Info.plist */,
				561E006B2E34904100BFFB5D /* GoogleService-Info.plist */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		C28058C6E36FC3F337B3F925 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A6E814F10D859D0594F1E3C2 /* APIKeys.swift */,
				C093F1FD032970F0D124F474 /* ImagePicker.swift */,
				11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */,
				2ECBC2D9E3F0886A6F5C4476 /* PermissionHandler.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		D463D5725897FDF54E6EABBC = {
			isa = PBXGroup;
			children = (
				BBEFB2E0D465492D3CC1B9F4 /* Application */,
				A1F9EAED5F5762EE342BAE4F /* Coordinator */,
				B4B9DBFF954BD5347C879108 /* Features */,
				A3C06954CFBF169AC85CF7F6 /* Models */,
				E8F39B9E25349F9BC1411826 /* Services */,
				C28058C6E36FC3F337B3F925 /* Utilities */,
				DDD72ED76DA11BC22FFC4895 /* Products */,
			);
			sourceTree = "<group>";
		};
		DDD72ED76DA11BC22FFC4895 /* Products */ = {
			isa = PBXGroup;
			children = (
				D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E8F39B9E25349F9BC1411826 /* Services */ = {
			isa = PBXGroup;
			children = (
				96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */,
				DA27F2449FA133E6C7BD796D /* GoogleVisionAPIService.swift */,
				DFC23B81C1836632A9C0C7D1 /* PantryService.swift */,
				CF40FDE398B1A6CE1FD2B0B7 /* RecipeGenerationService.swift */,
				3CA085D99E48956579051C8A /* ServiceContainer.swift */,
				701CB3D0687502DE6A6450B9 /* ShoppingListService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		930782F8BD78C73099163318 /* IngredientScanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 934DAD8BBAF2712F704AEE6A /* Build configuration list for PBXNativeTarget "IngredientScanner" */;
			buildPhases = (
				ADD5E749E813581FAABB57D6 /* Sources */,
				D58938D13C4BC5DD9A99C61C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = IngredientScanner;
			packageProductDependencies = (
				56ACDFC72E35F22E0055C756 /* FirebaseAuth */,
				56ACDFC82E35F22E0055C756 /* FirebaseCore */,
				56ACDFC92E35F22E0055C756 /* FirebaseFirestore */,
				56D50B112E3B3D8F008B59C3 /* GoogleSignIn */,
				56D50B122E3B3D8F008B59C3 /* GoogleSignInSwift */,
			);
			productName = IngredientScanner;
			productReference = D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2212BE8852EE267CB6C8FE89 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					930782F8BD78C73099163318 = {
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 3F5BB50E25893847BAF617DA /* Build configuration list for PBXProject "IngredientScanner" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = D463D5725897FDF54E6EABBC;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				56ACDFC62E35F22E0055C756 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				56D50B102E3B3D8F008B59C3 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			projectDirPath = "";
			projectRoot = "";
			targets = (
				930782F8BD78C73099163318 /* IngredientScanner */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D58938D13C4BC5DD9A99C61C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				190112BDFD568D7BD1682DCA /* Assets.xcassets in Resources */,
				561E006C2E34904100BFFB5D /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		ADD5E749E813581FAABB57D6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5ED14FC0EC3F3AA2E63FD535 /* APIKeys.swift in Sources */,
				470B030CFE6F3D8EF8AF9C97 /* App.swift in Sources */,
				88D2F764123B52301A3B70B8 /* AppCoordinator.swift in Sources */,
				4110690D016E8E8C862C9D91 /* BatchGeminiProcessingView.swift in Sources */,
				A080BC4B38E2E67D909ECC85 /* BatchGeminiProcessingViewModel.swift in Sources */,
				A39330ADD3AA43305DE381C4 /* BatchProcessingView.swift in Sources */,
				98B4CE1CB107DFB949C39140 /* BatchProcessingViewModel.swift in Sources */,
				C68C0438252E06DD37D44BC1 /* BatchVisionResultsView.swift in Sources */,
				2A34768F4DFE82C7F478BA00 /* BatchVisionResultsViewModel.swift in Sources */,
				89AFB66F259BE7565A5D1801 /* DebugView.swift in Sources */,
				6C6A417E7F5E0972303323D6 /* DebugViewModel.swift in Sources */,
				590EF66376A8348CBBBF4BCF /* GeminiAPIService.swift in Sources */,
				46D1AA5FBF4641ECC38CAB93 /* GeneratedRecipeDetailView.swift in Sources */,
				E684CC98841467E1D92339FE /* GoogleVisionAPIService.swift in Sources */,
				16947ACBFFE296C4C5608521 /* ImagePicker.swift in Sources */,
				9AC0D19AA92FEA4636B57E8D /* Ingredient.swift in Sources */,
				3BF985B6E33165CCCAB97988 /* MultiImagePicker.swift in Sources */,
				465AA90B58DBF295DF9729FB /* PantryCategory.swift in Sources */,
				3CEEAA16C0A38AD1F62E30E0 /* PantryService.swift in Sources */,
				9634C6A0A790DFCF8DB74308 /* PantryView.swift in Sources */,
				2009917FD1AE77EF5C26151D /* PantryViewModel.swift in Sources */,
				536BF110E8AD9928F8EA2EE1 /* PermissionHandler.swift in Sources */,
				286D3AD60207B07E1922ED46 /* Recipe.swift in Sources */,
				9AAB43E55D0D950A9774FB0D /* RecipeCardView.swift in Sources */,
				EC9C0184D659F23C2A4D1112 /* RecipeGenerationService.swift in Sources */,
				EDB1FCBE871A2FC42E1BB1B0 /* RecipeGeneratorView.swift in Sources */,
				E6D16E11F54A7B3AC607A39C /* RecipeGeneratorViewModel.swift in Sources */,
				300FF772D541C1E8D839037D /* ResultsView.swift in Sources */,
				4CAC1DE461273018EEC02DA7 /* ResultsViewModel.swift in Sources */,
				F9A41FBACF9CCA4A42169055 /* ServiceContainer.swift in Sources */,
				AB83BF354A5EDB9FB70257BE /* ShoppingListService.swift in Sources */,
				91C3FDCFC68D219BBE62D9FD /* ShoppingListView.swift in Sources */,
				C6EA4672D5D150BFFABE904E /* ShoppingListViewModel.swift in Sources */,
				7A3011B5F41A0480B47E8D1C /* StagingView.swift in Sources */,
				8C6699499ED137FE3BBEDE00 /* StagingViewModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A880B49E585BD8E0F7594CBB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = WJM5MTA2FG;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Application/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = anaesthesia.gtest;
				PRODUCT_NAME = IngredientScanner;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B03696EE21174C7DACC19C30 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = WJM5MTA2FG;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Application/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = anaesthesia.gtest;
				PRODUCT_NAME = IngredientScanner;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E99B563E6A920425D0D3542F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		F0F865AAC6BB2C1182B5E8D8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3F5BB50E25893847BAF617DA /* Build configuration list for PBXProject "IngredientScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F0F865AAC6BB2C1182B5E8D8 /* Debug */,
				E99B563E6A920425D0D3542F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		934DAD8BBAF2712F704AEE6A /* Build configuration list for PBXNativeTarget "IngredientScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B03696EE21174C7DACC19C30 /* Debug */,
				A880B49E585BD8E0F7594CBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		56ACDFC62E35F22E0055C756 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 12.0.0;
			};
		};
		56D50B102E3B3D8F008B59C3 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 9.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		56ACDFC72E35F22E0055C756 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 56ACDFC62E35F22E0055C756 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		56ACDFC82E35F22E0055C756 /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 56ACDFC62E35F22E0055C756 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
		56ACDFC92E35F22E0055C756 /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 56ACDFC62E35F22E0055C756 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		56D50B112E3B3D8F008B59C3 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 56D50B102E3B3D8F008B59C3 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		56D50B122E3B3D8F008B59C3 /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 56D50B102E3B3D8F008B59C3 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2212BE8852EE267CB6C8FE89 /* Project object */;
}
